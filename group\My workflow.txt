{"name": "Telegram Bot Workflow – Supabase Ready", "nodes": [{"parameters": {"updates": ["message"]}, "id": "telegramTriggerGroupMessage", "name": "<PERSON><PERSON><PERSON> (Group Messages)", "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.1, "position": [240, 300], "credentials": {"telegramApi": {"id": "telegram_bot_credentials", "name": "Telegram Bot API"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"leftValue": "={{ $json.message.message_thread_id }}", "rightValue": "101", "operation": {"type": "string", "operation": "equals"}}, {"leftValue": "={{ $json.message.message_thread_id }}", "rightValue": "102", "operation": {"type": "string", "operation": "equals"}}], "combinator": "or"}}, "id": "routeByTopicId", "name": "Route by Topic ID", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"chatId": "={{ $json.message.chat.id }}", "text": "This is the **Investments** topic. For your security, all personal investment details are handled in our private chat.", "replyMarkup": "inlineKeyboard", "inlineKeyboard": {"rows": [{"row": {"buttons": [{"text": "💰 View My Investment (Private)", "additionalFields": {"url": "https://t.me/YOUR_BOT_USERNAME?start=view_investment"}}, {"text": "➕ Make a New Deposit (Private)", "additionalFields": {"url": "https://t.me/YOUR_BOT_USERNAME?start=initiate_deposit"}}]}}]}}, "id": "sendInvestmentTopicMenu", "name": "Send Investment Topic Menu", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [680, 200], "credentials": {"telegramApi": {"id": "telegram_bot_credentials", "name": "Telegram Bot API"}}}, {"parameters": {"chatId": "={{ $json.message.chat.id }}", "text": "This is the **Roadmap** topic. Here you can find public information about our progress.", "replyMarkup": "inlineKeyboard", "inlineKeyboard": {"rows": [{"row": {"buttons": [{"text": "🗺️ Show Full Roadmap", "additionalFields": {"callbackData": "show_roadmap_public"}}]}}, {"row": {"buttons": [{"text": "🎯 What\\\\\\\\\\\\'s the Next Milestone?", "additionalFields": {"callbackData": "show_next_milestone_public"}}]}}]}}, "id": "sendRoadmapTopicMenu", "name": "Send Roadmap Topic Menu", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [680, 320], "credentials": {"telegramApi": {"id": "telegram_bot_credentials", "name": "Telegram Bot API"}}}, {"parameters": {"chatId": "={{ $json.message.chat.id }}", "text": "Welcome! How can I assist you?"}, "id": "sendGeneralTopicMenu", "name": "Send General <PERSON><PERSON>", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [680, 440], "credentials": {"telegramApi": {"id": "telegram_bot_credentials", "name": "Telegram Bot API"}}}, {"parameters": {"updates": ["message"]}, "id": "telegramTriggerPrivateChat", "name": "<PERSON><PERSON><PERSON> (Private Chat)", "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.1, "position": [240, 600], "credentials": {"telegramApi": {"id": "telegram_bot_credentials", "name": "Telegram Bot API"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"leftValue": "={{ $json.message.text }}", "rightValue": "/start view_investment", "operation": {"type": "string", "operation": "contains"}}, {"leftValue": "={{ $json.message.text }}", "rightValue": "/start initiate_deposit", "operation": {"type": "string", "operation": "contains"}}], "combinator": "or"}}, "id": "routeByStartPayload", "name": "Route by Start Payload", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [460, 600]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT * FROM public.users WHERE telegram_id = {{ $json.message.from.id }}"}, "id": "checkUserInDatabase", "name": "Check User in Database", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [680, 520], "credentials": {"postgres": {"id": "supabase_credentials", "name": "Supabase Connection"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"leftValue": "={{ $json.is_verified_investor }}", "rightValue": true, "operation": {"type": "boolean", "operation": "true"}}], "combinator": "and"}, "options": {}}, "id": "checkIfVerifiedInvestor", "name": "Check if Verified Investor", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [900, 520]}, {"parameters": {"chatId": "={{ $json.message.chat.id }}", "text": "Hello {{ $json.name }}!\n- Total Deposits: {{ $json.investment_details.deposits.length }}\n- Current Value: ${{ $json.investment_details.total_value_usd }}\n\nFor a detailed history, contact support."}, "id": "sendInvestmentDetails", "name": "Send Investment Details", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [1120, 420], "credentials": {"telegramApi": {"id": "telegram_bot_credentials", "name": "Telegram Bot API"}}}, {"parameters": {"chatId": "={{ $json.message.chat.id }}", "text": "Your account is not yet verified as an investor. Please contact support or provide your wallet address to link your account."}, "id": "sendNotVerifiedMessage", "name": "Send Not Verified Message", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [1120, 620], "credentials": {"telegramApi": {"id": "telegram_bot_credentials", "name": "Telegram Bot API"}}}, {"parameters": {"chatId": "={{ $json.message.chat.id }}", "text": "Select the amount to deposit:", "replyMarkup": "inlineKeyboard", "inlineKeyboard": {"rows": [{"row": {"buttons": [{"text": "$1,000", "additionalFields": {"callbackData": "pay_1000"}}, {"text": "$2,500", "additionalFields": {"callbackData": "pay_2500"}}]}}, {"row": {"buttons": [{"text": "$5,000", "additionalFields": {"callbackData": "pay_5000"}}]}}]}}, "id": "sendDepositOptions", "name": "Send Deposit Options", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [680, 720], "credentials": {"telegramApi": {"id": "telegram_bot_credentials", "name": "Telegram Bot API"}}}, {"parameters": {"updates": ["callback_query"]}, "id": "telegramTriggerCallbackQuery", "name": "<PERSON><PERSON><PERSON> (Callback Query)", "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.1, "position": [240, 900], "credentials": {"telegramApi": {"id": "telegram_bot_credentials", "name": "Telegram Bot API"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"leftValue": "={{ $json.callback_query.data }}", "rightValue": "pay_", "operation": {"type": "string", "operation": "startsWith"}}, {"leftValue": "={{ $json.callback_query.data }}", "rightValue": "show_roadmap_public", "operation": {"type": "string", "operation": "equals"}}, {"leftValue": "={{ $json.callback_query.data }}", "rightValue": "show_next_milestone_public", "operation": {"type": "string", "operation": "equals"}}], "combinator": "or"}, "options": {}}, "id": "routeByCallbackData", "name": "Route by Callback Data", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [460, 900]}, {"parameters": {"jsCode": "const d = $input.first().json.callback_query.data;\nconst usd = d.replace('pay_', '');\nreturn { json: { ...$input.first().json, usd, cents: parseInt(usd) * 100, uid: $input.first().json.callback_query.from.id, ts: Date.now() } };"}, "id": "processPaymentAmount", "name": "Process Payment Amount", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [680, 800]}, {"parameters": {"chatId": "={{ $json.callback_query.message.chat.id }}", "title": "ELOH Deposit", "description": "Secure investment deposit", "payload": "DAO-DEPOSIT-{{ $json.uid }}-{{ $json.ts }}", "providerToken": "YOUR_PAYMENT_PROVIDER_TOKEN_FROM_BOTFATHER", "currency": "USD", "prices": {"pricesValues": [{"label": "<PERSON><PERSON><PERSON><PERSON> Amou<PERSON>", "amount": "={{ $json.cents }}"}]}}, "id": "sendInvoice", "name": "Send Invoice", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [900, 800], "credentials": {"telegramApi": {"id": "telegram_bot_credentials", "name": "Telegram Bot API"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT * FROM public.project_roadmap ORDER BY id"}, "id": "getRoadmapData", "name": "Get Roadmap Data", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [680, 1000], "credentials": {"postgres": {"id": "supabase_credentials", "name": "Supabase Connection"}}}, {"parameters": {"jsCode": "const rows = $input.first().json;\nlet txt = '\\ud83d\\uddfa\\ufe0f **ELOH Processing Public Roadmap**\\n\\n';\nrows.forEach(r => txt += `- **${r.name}**: ${r.status}\\n`);\nreturn { json: { ...$input.first().json, formatted: txt } };"}, "id": "formatRoadmapText", "name": "Format Roadmap Text", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 1000]}, {"parameters": {"chatId": "={{ $json.callback_query.message.chat.id }}", "text": "={{ $json.formatted }}"}, "id": "showFullRoadmap", "name": "Show Full Roadmap", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [1120, 1000], "credentials": {"telegramApi": {"id": "telegram_bot_credentials", "name": "Telegram Bot API"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT * FROM public.project_roadmap WHERE status IN ('Upcoming','In Progress') ORDER BY id LIMIT 1"}, "id": "getNextMilestone", "name": "Get Next Milestone", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [680, 1200], "credentials": {"postgres": {"id": "supabase_credentials", "name": "Supabase Connection"}}}, {"parameters": {"chatId": "={{ $json.callback_query.message.chat.id }}", "text": "🎯 **Next Milestone**: {{ $json.name }}\n\n*Details*: {{ $json.details }}"}, "id": "showNextMilestone", "name": "Show Next Milestone", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [900, 1200], "credentials": {"telegramApi": {"id": "telegram_bot_credentials", "name": "Telegram Bot API"}}}, {"parameters": {"updates": ["pre_checkout_query"]}, "id": "telegramTriggerPreCheckoutQuery", "name": "<PERSON><PERSON><PERSON> (Pre-Checkout Query)", "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.1, "position": [240, 1400], "credentials": {"telegramApi": {"id": "telegram_bot_credentials", "name": "Telegram Bot API"}}}, {"parameters": {"operation": "answerPreCheckoutQuery", "preCheckoutQueryId": "={{ $json.pre_checkout_query.id }}", "ok": true}, "id": "answerPreCheckoutQuery", "name": "Answer Pre-Checkout Query", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [460, 1400], "credentials": {"telegramApi": {"id": "telegram_bot_credentials", "name": "Telegram Bot API"}}}, {"parameters": {"updates": ["successful_payment"]}, "id": "telegramTriggerSuccessfulPayment", "name": "<PERSON><PERSON><PERSON> (Successful Payment)", "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.1, "position": [240, 1600], "credentials": {"telegramApi": {"id": "telegram_bot_credentials", "name": "Telegram Bot API"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO public.payments (user_id, amount, currency, created_at, telegram_charge_id) VALUES ({{ $json.successful_payment.from.id }}, {{ $json.successful_payment.total_amount / 100 }}, '{{ $json.successful_payment.currency }}' , NOW(), '{{ $json.successful_payment.telegram_payment_charge_id }}' )"}, "id": "recordPayment", "name": "Record Payment", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [460, 1600], "credentials": {"postgres": {"id": "supabase_credentials", "name": "Supabase Connection"}}}, {"parameters": {"chatId": "={{ $json.successful_payment.from.id }}", "text": "✅ Payment of ${{ $json.successful_payment.total_amount / 100 }} {{ $json.successful_payment.currency }} received! Thank you for your investment."}, "id": "sendPaymentConfirmation", "name": "Send Payment Confirmation", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [680, 1600], "credentials": {"telegramApi": {"id": "telegram_bot_credentials", "name": "Telegram Bot API"}}}, {"parameters": {"interval": 1, "unit": "hours"}, "id": "cron<PERSON><PERSON>", "name": "<PERSON><PERSON> (Hourly)", "type": "n8n-nodes-base.cron", "typeVersion": 1.1, "position": [240, 1800]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT * FROM public.project_roadmap WHERE status = 'Upcoming' ORDER BY id LIMIT 1"}, "id": "getUpcomingMilestone", "name": "Get Upcoming Milestone", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [460, 1800], "credentials": {"postgres": {"id": "supabase_credentials", "name": "Supabase Connection"}}}, {"parameters": {"chatId": "YOUR_TELEGRAM_CHANNEL_ID", "text": "📢 **Milestone Update!**\n\nOur next milestone, **{{ $json.name }}**, is {{ $json.status }}.\n\n*Details*: {{ $json.details }}"}, "id": "sendMilestoneUpdate", "name": "Send Milestone Update", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [680, 1800], "credentials": {"telegramApi": {"id": "telegram_bot_credentials", "name": "Telegram Bot API"}}}], "connections": {"telegramTriggerGroupMessage": [{"node": "routeByTopicId", "type": "main", "index": 0}], "routeByTopicId": [[{"node": "sendInvestmentTopicMenu", "type": "main", "index": 0}], [{"node": "sendRoadmapTopicMenu", "type": "main", "index": 1}], [{"node": "sendGeneralTopicMenu", "type": "main", "index": 2}]], "telegramTriggerPrivateChat": [{"node": "routeByStartPayload", "type": "main", "index": 0}], "routeByStartPayload": [[{"node": "checkUserInDatabase", "type": "main", "index": 0}], [{"node": "sendDepositOptions", "type": "main", "index": 1}]], "checkUserInDatabase": [{"node": "checkIfVerifiedInvestor", "type": "main", "index": 0}], "checkIfVerifiedInvestor": [[{"node": "sendInvestmentDetails", "type": "main", "index": 0}], [{"node": "sendNotVerifiedMessage", "type": "main", "index": 1}]], "telegramTriggerCallbackQuery": [{"node": "routeByCallbackData", "type": "main", "index": 0}], "routeByCallbackData": [[{"node": "processPaymentAmount", "type": "main", "index": 0}], [{"node": "getRoadmapData", "type": "main", "index": 1}], [{"node": "getNextMilestone", "type": "main", "index": 2}]], "processPaymentAmount": [{"node": "sendInvoice", "type": "main", "index": 0}], "getRoadmapData": [{"node": "formatRoadmapText", "type": "main", "index": 0}], "formatRoadmapText": [{"node": "showFullRoadmap", "type": "main", "index": 0}], "getNextMilestone": [{"node": "showNextMilestone", "type": "main", "index": 0}], "telegramTriggerPreCheckoutQuery": [{"node": "answerPreCheckoutQuery", "type": "main", "index": 0}], "telegramTriggerSuccessfulPayment": [{"node": "recordPayment", "type": "main", "index": 0}], "recordPayment": [{"node": "sendPaymentConfirmation", "type": "main", "index": 0}], "cronJob": [{"node": "getUpcomingMilestone", "type": "main", "index": 0}], "getUpcomingMilestone": [{"node": "sendMilestoneUpdate", "type": "main", "index": 0}]}}