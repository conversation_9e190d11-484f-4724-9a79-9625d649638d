# n8n Switchboard Method for Telegram Bots

## What is the Switchboard Method?

The **Switchboard Method** is a workflow design pattern that uses a single **Switch node** as a central router to direct incoming messages to different workflow branches. This approach is much more reliable than using multiple IF nodes and complex routing logic.

## Why Use Switchboard Method?

### ❌ Problems with Your Current Approach:
- **Multiple IF nodes** create complex execution paths
- **Nested conditions** are hard to debug
- **Multiple triggers** can cause conflicts
- **Complex routing** leads to "execute" errors

### ✅ Benefits of Switchboard Method:
- **Single point of routing** - easier to debug
- **Cleaner workflow structure** - better maintainability  
- **Fewer execution errors** - more reliable
- **Easier to extend** - just add new switch cases

## How the New Workflow Works

### 1. **Single Trigger**
```json
"telegramTrigger": {
  "updates": ["message", "chat_member", "callback_query"]
}
```
- Handles ALL Telegram events in one trigger
- No conflicts between multiple triggers

### 2. **Central Switchboard**
```json
"mainSwitchboard": {
  "dataType": "string",
  "value1": "={{ $json.message?.text || $json.callback_query?.data || 'new_member' }}",
  "rules": {
    "rules": [
      { "value2": "/start", "output": 0 },
      { "value2": "/kick", "output": 1 },
      { "value2": "/pin", "output": 2 },
      { "value2": "show_roadmap_public", "output": 3 },
      { "value2": "view_investment", "output": 4 },
      { "value2": "new_member", "output": 5 }
    ]
  }
}
```

### 3. **Smart Message Detection**
The switchboard automatically detects:
- **Text messages**: `$json.message?.text`
- **Callback queries**: `$json.callback_query?.data`  
- **New members**: Falls back to `'new_member'`

### 4. **Output Routing**
Each switch output goes to a specific workflow branch:
- **Output 0**: Start command → Welcome message
- **Output 1**: Kick command → Admin validation → Kick user
- **Output 2**: Pin command → Admin validation → Pin message
- **Output 3**: Roadmap callback → Get & format roadmap
- **Output 4**: Investment query → Check user → Show investment
- **Output 5**: New member → Send welcome message

## Key Improvements Made

### 1. **Simplified Admin Commands**
- Single admin check node
- Reused for both `/kick` and `/pin` commands
- Proper error handling

### 2. **Better Error Handling**
- Uses optional chaining (`?.`) to prevent undefined errors
- Fallback values for missing data
- Cleaner expressions

### 3. **Optimized Database Queries**
- Fewer database calls
- Better query structure
- Proper error handling

### 4. **Consistent Credential Usage**
- All nodes use `telegram_credentials2`
- Matches your existing setup
- No placeholder credentials

## Setup Instructions

### 1. **Import the New Workflow**
1. Copy the content from `telegram-dao-bot-switchboard.json`
2. In n8n, go to **Workflows** → **Import from File**
3. Paste the JSON content

### 2. **Verify Credentials**
Make sure you have these credentials set up:
- `telegram_credentials2` (Telegram API)
- `supabase_credentials` (Supabase)

### 3. **Test the Workflow**
1. **Start with simple commands**: `/start`
2. **Test admin commands**: `/kick`, `/pin` (as admin)
3. **Test callbacks**: Click roadmap button
4. **Test investment**: Use private chat with `?start=view_investment`

## Debugging Tips

### 1. **Check Switch Output**
- Look at the switch node execution
- Verify which output path is taken
- Check the input value being matched

### 2. **Validate Expressions**
- Test expressions in the expression editor
- Use optional chaining (`?.`) for safety
- Add fallback values

### 3. **Monitor Database Queries**
- Check Supabase node outputs
- Verify query results
- Ensure proper data structure

## Extending the Workflow

To add new commands:

1. **Add to Switch Rules**:
```json
{
  "value2": "/newcommand",
  "output": 6
}
```

2. **Add Connection**:
```json
[
  {
    "node": "handleNewCommand",
    "type": "main",
    "index": 0
  }
]
```

3. **Create Handler Node**:
Add your new command handler node.

## Common Issues & Solutions

### Issue: "Cannot read properties of undefined"
**Solution**: Use optional chaining in expressions:
```javascript
// ❌ Bad
$json.message.text

// ✅ Good  
$json.message?.text || 'default'
```

### Issue: Switch not routing correctly
**Solution**: Check the input expression:
```javascript
// Make sure this covers all cases
$json.message?.text || $json.callback_query?.data || 'new_member'
```

### Issue: Admin commands not working
**Solution**: Verify admin check query and user permissions in database.

## Migration from Old Workflow

1. **Backup** your current workflow
2. **Import** the new switchboard workflow
3. **Test** each feature individually
4. **Update** any custom logic you had
5. **Deactivate** the old workflow once confirmed working

The switchboard method should resolve your execution errors and provide a much more stable foundation for your Telegram DAO bot.
