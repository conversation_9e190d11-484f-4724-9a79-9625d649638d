{"name": "ELOH DAO Bot - Professional Build", "nodes": [{"parameters": {"updates": ["message", "chat_member", "callback_query"], "additionalFields": {}}, "id": "05c15d2a-1af0-47fa-a8f7-1af8514e2285", "name": "<PERSON>eg<PERSON>", "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.2, "position": [-1040, -96], "webhookId": "76bfb192-83ad-4541-9197-36d47cf7049b", "credentials": {"telegramApi": {"id": "YOUR_TELEGRAM_CREDENTIALS_ID", "name": "Telegram account"}}}, {"parameters": {"values": {"string": [{"name": "botUsername", "value": "elohprocessingbot"}]}, "options": {}}, "id": "a69fdc0a-ae4c-4a7a-aa91-94cba4b5a088", "name": "Set Variables", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [-820, -96]}, {"parameters": {"dataType": "string", "value1": "={{ $json.message ? ($json.message.text.includes(' ') ? $json.message.text.split(' ')[0] : $json.message.text) : ($json.callback_query ? $json.callback_query.data : ($json.chat_member ? 'new_member' : '')) }}", "rules": {"rules": [{"value2": "/start"}, {"value2": "/kick", "output": 1}, {"value2": "/pin", "output": 1}, {"value2": "show_roadmap_public", "output": 2}, {"value2": "new_member", "output": 3}, {"value2": "/start view_investment", "output": 4}]}}, "id": "b4c7fbb0-ef5b-4515-8727-1317f76e4267", "name": "Main Switch", "type": "n8n-nodes-base.switch", "typeVersion": 1, "position": [-600, -96], "notes": "Routes all incoming events. Note that /kick and /pin go to the same output to be checked for admin rights first."}, {"parameters": {"chatId": "={{ $json.message.chat.id }}", "text": "Welcome to ELOH Processing DAO! 🚀", "replyMarkup": "inlineKeyboard", "inlineKeyboard": {"rows": [{"row": {"buttons": [{"text": "🗺️ Public Roadmap", "additionalFields": {"callbackData": "show_roadmap_public"}}]}}, {"row": {"buttons": [{"text": "💰 View My Investment (Private Chat)", "additionalFields": {"url": "={{ 'https://t.me/' + $node['Set Variables'].json.botUsername + '?start=view_investment' }}"}}]}}]}, "additionalFields": {}}, "id": "70178c2a-fc67-43aa-a202-f1f9f53b21df", "name": "Send Start Message", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-360, -220], "webhookId": "63e9fd13-fae6-4adf-8571-cbc80e56671e", "credentials": {"telegramApi": {"id": "YOUR_TELEGRAM_CREDENTIALS_ID", "name": "Telegram account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT * FROM public.project_roadmap ORDER BY id;", "options": {}}, "id": "getRoadmapData", "name": "Get Roadmap Data", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-360, 0], "credentials": {"supabase": {"id": "YOUR_SUPABASE_CREDENTIALS_ID", "name": "Supabase Credentials"}}}, {"parameters": {"jsCode": "const items = $input.all();\nlet formattedText = '🗺️ **ELOH Processing Public Roadmap**\\n\\n';\nfor (const item of items) {\n  formattedText += `- **${item.json.name}**: ${item.json.status}\\n`;\n}\n\n// CORRECTED LOGIC: Merge result with original data, don't replace it.\n$input.first().json.formatted_text = formattedText;\nreturn $input.first();"}, "id": "FormatRoadmapText", "name": "Format Roadmap Text", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-140, 0]}, {"parameters": {"chatId": "={{ $json.callback_query.message.chat.id }}", "text": "={{ $json.formatted_text }}", "additionalFields": {}}, "id": "23ab0933-dd50-4c66-bb0f-8f66c7e3fceb", "name": "Send Roadmap", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [80, 0], "webhookId": "65af357d-c0e8-4dc8-aa35-8f91762784a9", "credentials": {"telegramApi": {"id": "YOUR_TELEGRAM_CREDENTIALS_ID", "name": "Telegram account"}}}, {"parameters": {"chatId": "={{ $json.chat_member.chat.id }}", "text": "=Welcome @{{ $json.chat_member.new_chat_member.user.username }} to ELOH Processing DAO! 🎉\n\nPlease review our rules and start a private chat for investor features.", "replyMarkup": "inlineKeyboard", "inlineKeyboard": {"rows": [{"row": {"buttons": [{"text": "💬 Start Private Chat", "additionalFields": {"url": "={{ 'https://t.me/' + $node['Set Variables'].json.botUsername }}"}}]}}]}, "additionalFields": {}}, "id": "3bf43ea6-2552-4d7d-bdda-abf0b010579f", "name": "Send Welcome", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-360, 120], "webhookId": "9ae15ef7-31a5-4b20-a299-a7dbbdb3b941", "credentials": {"telegramApi": {"id": "YOUR_TELEGRAM_CREDENTIALS_ID", "name": "Telegram account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT * FROM public.users WHERE telegram_id = '{{ $json.message.from.id }}' LIMIT 1", "options": {}}, "id": "checkUserInDatabase", "name": "Check User in Database", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-360, 240], "credentials": {"supabase": {"id": "YOUR_SUPABASE_CREDENTIALS_ID", "name": "Supabase Credentials"}}}, {"parameters": {"conditions": {"options": {}, "conditions": [{"leftValue": "={{ $node[\"checkUserInDatabase\"].json[0]?.is_verified_investor }}", "rightValue": true, "operation": {"type": "boolean", "operation": "true"}}]}, "options": {}}, "id": "checkIfVerifiedInvestor", "name": "Check if Verified Investor", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-140, 240]}, {"parameters": {"chatId": "={{ $json.message.chat.id }}", "text": "=Hello {{ $node[\"checkUserInDatabase\"].json[0].name }}! Your current investment value is: ${{ $node[\"checkUserInDatabase\"].json[0].investment_details.total_value_usd }}."}, "id": "sendInvestmentDetails", "name": "Send Investment Details", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [80, 140], "credentials": {"telegramApi": {"id": "YOUR_TELEGRAM_CREDENTIALS_ID", "name": "Telegram account"}}}, {"parameters": {"chatId": "={{ $json.message.chat.id }}", "text": "Your Telegram account is not yet linked to a verified investor profile. Please contact support."}, "id": "sendNotVerifiedMessage", "name": "Send Not Verified Message", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [80, 340], "credentials": {"telegramApi": {"id": "YOUR_TELEGRAM_CREDENTIALS_ID", "name": "Telegram account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT role FROM public.users WHERE telegram_id = '{{$json.message.from.id}}' AND role = 'admin' LIMIT 1;", "options": {}}, "id": "checkIfUserIsAdmin", "name": "Check if User is Admin", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-360, -100], "credentials": {"supabase": {"id": "YOUR_SUPABASE_CREDENTIALS_ID", "name": "Supabase Credentials"}}}, {"parameters": {"conditions": {"options": {}, "conditions": [{"leftValue": "={{$node['checkIfUserIsAdmin'].json.length}}", "rightValue": 0, "operation": {"type": "number", "operation": "larger"}}]}, "options": {}}, "id": "IFAdmin", "name": "IF Admin", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-140, -100]}, {"parameters": {"chatId": "={{ $json.message.chat.id }}", "text": "Admin command received. This feature is coming soon!", "additionalFields": {}}, "id": "AdminActionPlaceholder", "name": "Admin Action Placeholder", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [80, -100], "credentials": {"telegramApi": {"id": "YOUR_TELEGRAM_CREDENTIALS_ID", "name": "Telegram account"}}}], "pinData": {}, "connections": {"Telegram Trigger": {"main": [[{"node": "Set Variables", "type": "main", "index": 0}]]}, "Set Variables": {"main": [[{"node": "Main Switch", "type": "main", "index": 0}]]}, "Main Switch": {"main": [[{"node": "Send Start Message", "type": "main", "index": 0}], [{"node": "checkIfUserIsAdmin", "type": "main", "index": 0}], [{"node": "getRoadmapData", "type": "main", "index": 0}], [{"node": "Send Welcome", "type": "main", "index": 0}], [{"node": "checkUserInDatabase", "type": "main", "index": 0}]]}, "getRoadmapData": {"main": [[{"node": "FormatRoadmapText", "type": "main", "index": 0}]]}, "FormatRoadmapText": {"main": [[{"node": "Send Roadmap", "type": "main", "index": 0}]]}, "checkUserInDatabase": {"main": [[{"node": "checkIfVerifiedInvestor", "type": "main", "index": 0}]]}, "checkIfVerifiedInvestor": {"main": [[{"node": "sendInvestmentDetails", "type": "main", "index": 0}], [{"node": "sendNotVerifiedMessage", "type": "main", "index": 0}]]}, "checkIfUserIsAdmin": {"main": [[{"node": "IFAdmin", "type": "main", "index": 0}]]}, "IFAdmin": {"main": [[{"node": "AdminActionPlaceholder", "type": "main", "index": 0}], []]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "b8a8b2e1-583b-419b-a01b-c6a67f780875", "meta": {"templateCredsSetupCompleted": true, "instanceId": "1f52f14222153875e5cd754160d07a91bc57a3dd7ee7266d4266eae0b531fc56"}, "id": "kUf6W9D5Y9PZ2sC7", "tags": [{"createdAt": "2025-08-06T12:34:09.665Z", "updatedAt": "2025-08-06T12:34:09.665Z", "id": "EKr6mdBmEMIyCP37", "name": "ELOH"}]}