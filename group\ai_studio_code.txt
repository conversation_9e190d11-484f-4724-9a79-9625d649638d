{"name": "Telegram DAO Bot - Full Admin Workflow (Corrected)", "nodes": [{"parameters": {"updates": ["message", "chat_member"]}, "id": "telegramTriggerGroupEvents", "name": "<PERSON><PERSON><PERSON> (Group Events)", "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.1, "position": [200, 720], "credentials": {"telegramApi": {"id": "telegram_bot_credentials", "name": "Telegram Bot API"}}}, {"parameters": {"values": {"string": [{"name": "botUsername", "value": "YOUR_ACTUAL_BOT_USERNAME_HERE"}, {"name": "notificationChannelId", "value": "YOUR_ACTUAL_CHANNEL_ID_HERE"}]}, "options": {}}, "id": "SetWorkflowVariables", "name": "Set Workflow Variables", "type": "n8n-nodes-base.set", "typeVersion": 1.1, "position": [420, 720]}, {"parameters": {"conditions": {"options": {}, "conditions": [{"leftValue": "={{$json.chat_member}}", "operation": {"type": "boolean", "operation": "exists"}}, {"leftValue": "={{$json.message.text}}", "rightValue": "/", "operation": {"type": "string", "operation": "startsWith"}}]}, "options": {}}, "id": "routeEventType", "name": "Route Event Type", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [640, 720]}, {"parameters": {"chatId": "={{$json.chat_member.chat.id}}", "text": "=Welcome @{{$json.chat_member.new_chat_member.user.username}} to the ELOH Processing DAO!\n\nPlease review our rules in the pinned message. To get full access, please verify your investor status by interacting with me in a private chat.", "replyMarkup": "inlineKeyboard", "inlineKeyboard": {"rows": [{"row": {"buttons": [{"text": "Start Verification (Private Chat)", "additionalFields": {"url": "={{ 'https://t.me/' + $node[\"SetWorkflowVariables\"].json.botUsername + '?start=verify_me' }}"}}]}}]}}, "id": "sendWelcomeMessage", "name": "Send Welcome Message", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [860, 620], "credentials": {"telegramApi": {"id": "telegram_bot_credentials", "name": "Telegram Bot API"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT role FROM public.users WHERE telegram_id = '{{$json.message.from.id}}' AND role = 'admin' LIMIT 1;"}, "id": "checkIfUserIsAdmin", "name": "Check if User is Admin", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [860, 720], "credentials": {"postgres": {"id": "supabase_credentials", "name": "Supabase Connection"}}}, {"parameters": {"conditions": {"options": {}, "conditions": [{"leftValue": "={{$node['checkIfUserIsAdmin'].json.length}}", "rightValue": 0, "operation": {"type": "number", "operation": "larger"}}]}, "options": {}}, "id": "IFAdmin", "name": "IF Admin", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1080, 720]}, {"parameters": {"conditions": {"options": {}, "conditions": [{"leftValue": "={{$json.message.text}}", "rightValue": "/kick", "operation": {"type": "string", "operation": "startsWith"}}, {"leftValue": "={{$json.message.text}}", "rightValue": "/pin", "operation": {"type": "string", "operation": "startsWith"}}]}, "options": {}}, "id": "routeAdminCommand", "name": "Route Admin Command", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1300, 720]}, {"parameters": {"operation": "kickChatMember", "chatId": "={{$json.message.chat.id}}", "userId": "={{$json.message.reply_to_message.from.id}}", "options": {}}, "id": "kickUser", "name": "Kick User", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [1520, 620], "credentials": {"telegramApi": {"id": "telegram_bot_credentials", "name": "Telegram Bot API"}}}, {"parameters": {"operation": "pinChatMessage", "chatId": "={{$json.message.chat.id}}", "messageId": "={{$json.message.reply_to_message.message_id}}", "options": {"disableNotification": false}}, "id": "pinMessage", "name": "Pin Message", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [1520, 820], "credentials": {"telegramApi": {"id": "telegram_bot_credentials", "name": "Telegram Bot API"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT investments_topic_id, roadmap_topic_id FROM public.group_config LIMIT 1;"}, "id": "GetTopicConfig", "name": "Get Topic Config", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [860, 840], "credentials": {"postgres": {"id": "supabase_credentials", "name": "Supabase Connection"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true}, "conditions": [{"leftValue": "={{ $json.message.message_thread_id }}", "rightValue": "={{ $node[\"GetTopicConfig\"].json[0].investments_topic_id }}", "operation": {"type": "string", "operation": "equals"}}, {"leftValue": "={{ $json.message.message_thread_id }}", "rightValue": "={{ $node[\"GetTopicConfig\"].json[0].roadmap_topic_id }}", "operation": {"type": "string", "operation": "equals"}}]}, "options": {}}, "id": "routeByTopicId", "name": "Route by Topic ID", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1080, 840]}, {"parameters": {"chatId": "={{ $json.message.chat.id }}", "text": "This is the **Investments** topic. For your security, all personal investment details are handled in our private chat.", "replyMarkup": "inlineKeyboard", "inlineKeyboard": {"rows": [{"row": {"buttons": [{"text": "💰 View My Investment (Private)", "additionalFields": {"url": "={{ 'https://t.me/' + $node[\"SetWorkflowVariables\"].json.botUsername + '?start=view_investment' }}"}}, {"text": "➕ Make a New Deposit (Private)", "additionalFields": {"url": "={{ 'https://t.me/' + $node[\"SetWorkflowVariables\"].json.botUsername + '?start=initiate_deposit' }}"}}]}}]}}, "id": "sendInvestmentTopicMenu", "name": "Send Investment Topic Menu", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [1300, 940], "credentials": {"telegramApi": {"id": "telegram_bot_credentials", "name": "Telegram Bot API"}}}, {"parameters": {"chatId": "={{ $json.message.chat.id }}", "text": "This is the **Roadmap** topic. Here you can find public information about our progress.", "replyMarkup": "inlineKeyboard", "inlineKeyboard": {"rows": [{"row": {"buttons": [{"text": "🗺️ Show Full Roadmap", "additionalFields": {"callbackData": "show_roadmap_public"}}]}}, {"row": {"buttons": [{"text": "🎯 What's the Next Milestone?", "additionalFields": {"callbackData": "show_next_milestone_public"}}]}}]}}, "id": "sendRoadmapTopicMenu", "name": "Send Roadmap Topic Menu", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [1300, 1060], "credentials": {"telegramApi": {"id": "telegram_bot_credentials", "name": "Telegram Bot API"}}}, {"parameters": {"chatId": "={{ $json.message.chat.id }}", "text": "Welcome! This is a general discussion topic. How can I assist you?", "options": {}}, "id": "sendGeneralTopicMenu", "name": "Send General <PERSON><PERSON>", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [1300, 1180], "credentials": {"telegramApi": {"id": "telegram_bot_credentials", "name": "Telegram Bot API"}}}, {"parameters": {"updates": ["message"]}, "id": "telegramTriggerPrivateChat", "name": "<PERSON><PERSON><PERSON> (Private Chat)", "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.1, "position": [200, 1320], "credentials": {"telegramApi": {"id": "telegram_bot_credentials", "name": "Telegram Bot API"}}}, {"parameters": {"conditions": {"options": {}, "conditions": [{"leftValue": "={{ $json.message.text }}", "rightValue": "/start view_investment", "operation": {"type": "string", "operation": "contains"}}, {"leftValue": "={{ $json.message.text }}", "rightValue": "/start initiate_deposit", "operation": {"type": "string", "operation": "contains"}}]}, "options": {}}, "id": "routeByStartPayload", "name": "Route by Start Payload", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [420, 1320]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT * FROM public.users WHERE telegram_id = '{{ $json.message.from.id }}' LIMIT 1"}, "id": "checkUserInDatabase", "name": "Check User in Database", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [640, 1240], "credentials": {"postgres": {"id": "supabase_credentials", "name": "Supabase Connection"}}}, {"parameters": {"conditions": {"options": {}, "conditions": [{"leftValue": "={{ $node[\"checkUserInDatabase\"].json[0]?.is_verified_investor }}", "rightValue": true, "operation": {"type": "boolean", "operation": "true"}}]}, "options": {}}, "id": "checkIfVerifiedInvestor", "name": "Check if Verified Investor", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [860, 1240]}, {"parameters": {"chatId": "={{ $json.message.chat.id }}", "text": "=Hello {{ $node[\"checkUserInDatabase\"].json[0].name }}! Here are your investment details:\n\n- Current Value: ${{ $node[\"checkUserInDatabase\"].json[0].investment_details.total_value_usd }}\n\nFor a detailed history, please contact support."}, "id": "sendInvestmentDetails", "name": "Send Investment Details", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [1080, 1140], "credentials": {"telegramApi": {"id": "telegram_bot_credentials", "name": "Telegram Bot API"}}}, {"parameters": {"chatId": "={{ $json.message.chat.id }}", "text": "Your Telegram account is not yet linked to a verified investor profile. Please contact support for assistance."}, "id": "sendNotVerifiedMessage", "name": "Send Not Verified Message", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [1080, 1340], "credentials": {"telegramApi": {"id": "telegram_bot_credentials", "name": "Telegram Bot API"}}}, {"parameters": {"chatId": "={{ $json.message.chat.id }}", "text": "Please select the amount you wish to deposit:", "replyMarkup": "inlineKeyboard", "inlineKeyboard": {"rows": [{"row": {"buttons": [{"text": "$1,000", "additionalFields": {"callbackData": "pay_1000"}}, {"text": "$2,500", "additionalFields": {"callbackData": "pay_2500"}}]}}, {"row": {"buttons": [{"text": "$5,000", "additionalFields": {"callbackData": "pay_5000"}}]}}]}}, "id": "sendDepositOptions", "name": "Send Deposit Options", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [640, 1440], "credentials": {"telegramApi": {"id": "telegram_bot_credentials", "name": "Telegram Bot API"}}}, {"parameters": {"updates": ["callback_query"]}, "id": "telegramTriggerCallbackQuery", "name": "<PERSON><PERSON><PERSON> (Callback Query)", "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.1, "position": [200, 1620], "credentials": {"telegramApi": {"id": "telegram_bot_credentials", "name": "Telegram Bot API"}}}, {"parameters": {"conditions": {"options": {}, "conditions": [{"leftValue": "={{ $json.callback_query.data }}", "rightValue": "pay_", "operation": {"type": "string", "operation": "startsWith"}}, {"leftValue": "={{ $json.callback_query.data }}", "rightValue": "show_roadmap_public", "operation": {"type": "string", "operation": "equals"}}, {"leftValue": "={{ $json.callback_query.data }}", "rightValue": "show_next_milestone_public", "operation": {"type": "string", "operation": "equals"}}]}, "options": {}}, "id": "routeByCallbackData", "name": "Route by Callback Data", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [420, 1620]}, {"parameters": {"jsCode": "const data = $input.first().json.callback_query.data;\nconst amountUsd = parseInt(data.replace('pay_', ''));\n\n$input.first().json.amount_usd = amountUsd;\n$input.first().json.amount_cents = amountUsd * 100;\n$input.first().json.user_id = $input.first().json.callback_query.from.id;\n$input.first().json.timestamp = Date.now();\n\nreturn $input.first().json;"}, "id": "processPaymentAmount", "name": "Process Payment Amount", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [640, 1520]}, {"parameters": {"chatId": "={{ $json.callback_query.message.chat.id }}", "title": "ELOH Processing Investment Deposit", "description": "Secure investment into sustainable mining operations.", "payload": "={{ 'DAO-DEPOSIT-' + $json.user_id + '-' + $json.timestamp }}", "providerToken": "", "currency": "USD", "prices": {"pricesValues": [{"label": "<PERSON><PERSON><PERSON><PERSON> Amou<PERSON>", "amount": "={{ $json.amount_cents }}"}]}, "options": {}}, "id": "sendInvoice", "name": "Send Invoice", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [860, 1520], "credentials": {"telegramApi": {"id": "telegram_bot_credentials", "name": "Telegram Bot API"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT * FROM public.project_roadmap ORDER BY id;"}, "id": "getRoadmapData", "name": "Get Roadmap Data", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [640, 1720], "credentials": {"postgres": {"id": "supabase_credentials", "name": "Supabase Connection"}}}, {"parameters": {"jsCode": "const roadmapItems = $input.first().json;\nlet formattedText = '🗺️ **ELOH Processing Public Roadmap**\\n\\n';\nfor (const item of roadmapItems) {\n  formattedText += `- **${item.name}**: ${item.status}\\n`;\n}\nreturn { formatted_text: formattedText };"}, "id": "formatRoadmapText", "name": "Format Roadmap Text", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [860, 1720]}, {"parameters": {"chatId": "={{ $node[\"routeByCallbackData\"].json.callback_query.message.chat.id }}", "text": "={{ $node[\"formatRoadmapText\"].json.formatted_text }}"}, "id": "showFullRoadmap", "name": "Show Full Roadmap", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [1080, 1720], "credentials": {"telegramApi": {"id": "telegram_bot_credentials", "name": "Telegram Bot API"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT * FROM public.project_roadmap WHERE status IN ('Upcoming', 'In Progress') ORDER BY id ASC LIMIT 1;"}, "id": "getNextMilestone", "name": "Get Next Milestone", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [640, 1920], "credentials": {"postgres": {"id": "supabase_credentials", "name": "Supabase Connection"}}}, {"parameters": {"chatId": "={{ $node[\"routeByCallbackData\"].json.callback_query.message.chat.id }}", "text": "=🎯 **Next Milestone**: {{ $node[\"getNextMilestone\"].json[0].name }}\n\n*Details*: {{ $node[\"getNextMilestone\"].json[0].details }}"}, "id": "showNextMilestone", "name": "Show Next Milestone", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [860, 1920], "credentials": {"telegramApi": {"id": "telegram_bot_credentials", "name": "Telegram Bot API"}}}, {"parameters": {"updates": ["pre_checkout_query"]}, "id": "telegramTriggerPreCheckoutQuery", "name": "<PERSON><PERSON><PERSON> (Pre-Checkout Query)", "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.1, "position": [200, 2120], "credentials": {"telegramApi": {"id": "telegram_bot_credentials", "name": "Telegram Bot API"}}}, {"parameters": {"operation": "answerPreCheckoutQuery", "preCheckoutQueryId": "={{ $json.pre_checkout_query.id }}", "ok": true}, "id": "answerPreCheckoutQuery", "name": "Answer Pre-Checkout Query", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [420, 2120], "credentials": {"telegramApi": {"id": "telegram_bot_credentials", "name": "Telegram Bot API"}}}, {"parameters": {"updates": ["successful_payment"]}, "id": "telegramTriggerSuccessfulPayment", "name": "<PERSON><PERSON><PERSON> (Successful Payment)", "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.1, "position": [200, 2320], "credentials": {"telegramApi": {"id": "telegram_bot_credentials", "name": "Telegram Bot API"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "=INSERT INTO public.payments (user_id, amount, currency, created_at, telegram_charge_id) VALUES ( '{{ $json.message.successful_payment.from.id }}', {{ $json.message.successful_payment.total_amount }}, '{{ $json.message.successful_payment.currency }}', NOW(), '{{ $json.message.successful_payment.telegram_payment_charge_id }}' ) RETURNING *"}, "id": "recordPayment", "name": "Record Payment", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [420, 2320], "credentials": {"postgres": {"id": "supabase_credentials", "name": "Supabase Connection"}}}, {"parameters": {"chatId": "={{ $json.message.successful_payment.from.id }}", "text": "=✅ Payment of ${{ $json.message.successful_payment.total_amount / 100 }} {{ $json.message.successful_payment.currency }} received! Thank you for your investment."}, "id": "sendPaymentConfirmation", "name": "Send Payment Confirmation", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [640, 2320], "credentials": {"telegramApi": {"id": "telegram_bot_credentials", "name": "Telegram Bot API"}}}], "connections": {"telegramTriggerGroupEvents": [{"node": "SetWorkflowVariables", "type": "main", "index": 0}], "SetWorkflowVariables": [{"node": "routeEventType", "type": "main", "index": 0}], "routeEventType": [[{"node": "sendWelcomeMessage", "type": "main", "index": 0}], [{"node": "checkIfUserIsAdmin", "type": "main", "index": 0}], [{"node": "GetTopicConfig", "type": "main", "index": 0}]], "checkIfUserIsAdmin": [{"node": "IFAdmin", "type": "main", "index": 0}], "IFAdmin": [[{"node": "routeAdminCommand", "type": "main", "index": 0}]], "routeAdminCommand": [[{"node": "kickUser", "type": "main", "index": 0}], [{"node": "pinMessage", "type": "main", "index": 0}]], "GetTopicConfig": [{"node": "routeByTopicId", "type": "main", "index": 0}], "routeByTopicId": [[{"node": "sendInvestmentTopicMenu", "type": "main", "index": 0}], [{"node": "sendRoadmapTopicMenu", "type": "main", "index": 0}], [{"node": "sendGeneralTopicMenu", "type": "main", "index": 0}]], "telegramTriggerPrivateChat": [{"node": "routeByStartPayload", "type": "main", "index": 0}], "routeByStartPayload": [[{"node": "checkUserInDatabase", "type": "main", "index": 0}], [{"node": "sendDepositOptions", "type": "main", "index": 0}]], "checkUserInDatabase": [{"node": "checkIfVerifiedInvestor", "type": "main", "index": 0}], "checkIfVerifiedInvestor": [[{"node": "sendInvestmentDetails", "type": "main", "index": 0}], [{"node": "sendNotVerifiedMessage", "type": "main", "index": 0}]], "telegramTriggerCallbackQuery": [{"node": "routeByCallbackData", "type": "main", "index": 0}], "routeByCallbackData": [[{"node": "processPaymentAmount", "type": "main", "index": 0}], [{"node": "getRoadmapData", "type": "main", "index": 0}], [{"node": "getNextMilestone", "type": "main", "index": 0}]], "processPaymentAmount": [{"node": "sendInvoice", "type": "main", "index": 0}], "getRoadmapData": [{"node": "formatRoadmapText", "type": "main", "index": 0}], "formatRoadmapText": [{"node": "showFullRoadmap", "type": "main", "index": 0}], "getNextMilestone": [{"node": "showNextMilestone", "type": "main", "index": 0}], "telegramTriggerPreCheckoutQuery": [{"node": "answerPreCheckoutQuery", "type": "main", "index": 0}], "telegramTriggerSuccessfulPayment": [{"node": "recordPayment", "type": "main", "index": 0}], "recordPayment": [{"node": "sendPaymentConfirmation", "type": "main", "index": 0}]}}