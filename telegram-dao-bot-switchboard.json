{"name": "Telegram DAO Bot - Switchboard Method", "nodes": [{"parameters": {"updates": ["message", "chat_member", "callback_query"]}, "id": "telegramTrigger", "name": "<PERSON>eg<PERSON>", "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.2, "position": [240, 300], "credentials": {"telegramApi": {"id": "telegram_credentials2", "name": "Telegram Credentials"}}}, {"parameters": {"values": {"string": [{"name": "botUsername", "value": "elohprocessingbot"}]}}, "id": "setGlobalVariables", "name": "Set Global Variables", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"dataType": "string", "value1": "={{ $json.message?.text || $json.callback_query?.data || 'new_member' }}", "rules": {"rules": [{"value2": "/start", "output": 0}, {"value2": "/kick", "output": 1}, {"value2": "/pin", "output": 2}, {"value2": "show_roadmap_public", "output": 3}, {"value2": "view_investment", "output": 4}, {"value2": "new_member", "output": 5}]}}, "id": "mainSwitchboard", "name": "Main Switchboard", "type": "n8n-nodes-base.switch", "typeVersion": 1, "position": [680, 300]}, {"parameters": {"chatId": "={{ $json.message?.chat?.id || $json.callback_query?.message?.chat?.id }}", "text": "Welcome to ELOH Processing DAO! 🚀\n\nChoose an option:", "replyMarkup": "inlineKeyboard", "inlineKeyboard": {"rows": [{"row": {"buttons": [{"text": "💰 View Investment", "additionalFields": {"url": "={{ 'https://t.me/' + $node['setGlobalVariables'].json.botUsername + '?start=view_investment' }}"}}]}}, {"row": {"buttons": [{"text": "🗺️ Roadmap", "additionalFields": {"callbackData": "show_roadmap_public"}}]}}]}}, "id": "sendStartMessage", "name": "Send Start Message", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [900, 100], "credentials": {"telegramApi": {"id": "telegram_credentials2", "name": "Telegram Credentials"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT role FROM public.users WHERE telegram_id = '{{ $json.message.from.id }}' AND role = 'admin' LIMIT 1;", "options": {}}, "id": "checkAdminStatus", "name": "Check Admin Status", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [1120, 200], "credentials": {"supabase": {"id": "supabase_credentials", "name": "Supabase Credentials"}}}, {"parameters": {"conditions": {"conditions": [{"leftValue": "={{ $node['checkAdminStatus'].json.length }}", "rightValue": 0, "operation": "larger"}]}}, "id": "validateAdmin", "name": "Validate <PERSON>min", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1340, 200]}, {"parameters": {"operation": "kickChatMember", "chatId": "={{ $json.message.chat.id }}", "userId": "={{ $json.message.reply_to_message.from.id }}"}, "id": "<PERSON><PERSON><PERSON>", "name": "Execute Kick", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [1560, 150], "credentials": {"telegramApi": {"id": "telegram_credentials2", "name": "Telegram Credentials"}}}, {"parameters": {"operation": "pinChatMessage", "chatId": "={{ $json.message.chat.id }}", "messageId": "={{ $json.message.reply_to_message.message_id }}"}, "id": "executePin", "name": "Execute Pin", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [1560, 250], "credentials": {"telegramApi": {"id": "telegram_credentials2", "name": "Telegram Credentials"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT * FROM public.project_roadmap ORDER BY id;", "options": {}}, "id": "getRoadmapData", "name": "Get Roadmap Data", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [900, 400], "credentials": {"supabase": {"id": "supabase_credentials", "name": "Supabase Credentials"}}}, {"parameters": {"jsCode": "const items = $input.all();\nlet formattedText = '🗺️ **ELOH Processing Public Roadmap**\\n\\n';\nfor (const item of items) {\n  formattedText += `- **${item.json.name}**: ${item.json.status}\\n`;\n}\n\nreturn [{\n  json: {\n    ...($input.first().json),\n    formatted_text: formattedText\n  }\n}];"}, "id": "formatRoadmap", "name": "Format Roadmap", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1120, 400]}, {"parameters": {"chatId": "={{ $json.callback_query.message.chat.id }}", "text": "={{ $json.formatted_text }}"}, "id": "sendRoadmap", "name": "Send Roadmap", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [1340, 400], "credentials": {"telegramApi": {"id": "telegram_credentials2", "name": "Telegram Credentials"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT * FROM public.users WHERE telegram_id = '{{ $json.message.from.id }}' LIMIT 1", "options": {}}, "id": "getUserData", "name": "Get User Data", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [900, 500], "credentials": {"supabase": {"id": "supabase_credentials", "name": "Supabase Credentials"}}}, {"parameters": {"conditions": {"conditions": [{"leftValue": "={{ $node['getUserData'].json[0]?.is_verified_investor }}", "rightValue": true, "operation": "equal", "dataType": "boolean"}]}}, "id": "checkInvestorStatus", "name": "Check Investor Status", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1120, 500]}, {"parameters": {"chatId": "={{ $json.message.chat.id }}", "text": "=Hello {{ $node['getUserData'].json[0].name }}! 💰\n\nYour current investment value: ${{ $node['getUserData'].json[0].investment_details.total_value_usd }}"}, "id": "sendInvestmentInfo", "name": "Send Investment Info", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [1340, 450], "credentials": {"telegramApi": {"id": "telegram_credentials2", "name": "Telegram Credentials"}}}, {"parameters": {"chatId": "={{ $json.message.chat.id }}", "text": "❌ Your account is not linked to a verified investor profile. Please contact support."}, "id": "sendNotVerified", "name": "Send Not Verified", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [1340, 550], "credentials": {"telegramApi": {"id": "telegram_credentials2", "name": "Telegram Credentials"}}}, {"parameters": {"chatId": "={{ $json.chat_member.chat.id }}", "text": "=Welcome @{{ $json.chat_member.new_chat_member.user.username }} to ELOH Processing DAO! 🎉\n\nPlease review our rules and start a private chat for investor features.", "replyMarkup": "inlineKeyboard", "inlineKeyboard": {"rows": [{"row": {"buttons": [{"text": "💬 Start Private Chat", "additionalFields": {"url": "={{ 'https://t.me/' + $node['setGlobalVariables'].json.botUsername }}"}}]}}]}}, "id": "sendWelcome", "name": "Send Welcome", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [900, 600], "credentials": {"telegramApi": {"id": "telegram_credentials2", "name": "Telegram Credentials"}}}], "connections": {"telegramTrigger": {"main": [[{"node": "setGlobalVariables", "type": "main", "index": 0}]]}, "setGlobalVariables": {"main": [[{"node": "mainSwitchboard", "type": "main", "index": 0}]]}, "mainSwitchboard": {"main": [[{"node": "sendStartMessage", "type": "main", "index": 0}], [{"node": "checkAdminStatus", "type": "main", "index": 0}], [{"node": "checkAdminStatus", "type": "main", "index": 0}], [{"node": "getRoadmapData", "type": "main", "index": 0}], [{"node": "getUserData", "type": "main", "index": 0}], [{"node": "sendWelcome", "type": "main", "index": 0}]]}, "checkAdminStatus": {"main": [[{"node": "validateAdmin", "type": "main", "index": 0}]]}, "validateAdmin": {"main": [[{"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}, {"node": "executePin", "type": "main", "index": 0}], []]}, "getRoadmapData": {"main": [[{"node": "formatRoadmap", "type": "main", "index": 0}]]}, "formatRoadmap": {"main": [[{"node": "sendRoadmap", "type": "main", "index": 0}]]}, "getUserData": {"main": [[{"node": "checkInvestorStatus", "type": "main", "index": 0}]]}, "checkInvestorStatus": {"main": [[{"node": "sendInvestmentInfo", "type": "main", "index": 0}], [{"node": "sendNotVerified", "type": "main", "index": 0}]]}}}