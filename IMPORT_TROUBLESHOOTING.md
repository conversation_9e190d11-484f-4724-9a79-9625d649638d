# n8n Workflow Import Troubleshooting

## Error: "Could not find property option"

This error occurs when there are property mismatches or version incompatibilities in the workflow JSON.

## What I Fixed

### 1. **Node Version Compatibility**
- ✅ Changed Switch node from `typeVersion: 3` to `typeVersion: 1`
- ✅ Changed Set node from `typeVersion: 3` to `typeVersion: 1`  
- ✅ Changed IF nodes from `typeVersion: 2` to `typeVersion: 1`

### 2. **Property Structure Issues**
- ✅ Removed `options.allMatchingOutputs` from Switch node
- ✅ Simplified IF node conditions structure
- ✅ Fixed operation syntax in IF nodes

### 3. **Expression Compatibility**
- ✅ Changed `?.` optional chaining to traditional conditional expressions
- ✅ Used more compatible expression syntax

## Two Workflow Options

### Option 1: Fixed Original (`telegram-dao-bot-switchboard.json`)
- Full-featured workflow with database integration
- All original functionality preserved
- Fixed compatibility issues

### Option 2: Simple Version (`telegram-dao-bot-simple.json`)
- Minimal, guaranteed-to-work version
- Basic switchboard pattern
- No database dependencies
- Perfect for testing the concept

## Import Instructions

### Try Simple Version First:
1. Open `telegram-dao-bot-simple.json`
2. Copy the entire JSON content
3. In n8n: **Workflows** → **Import from File**
4. Paste the content and import

### If Simple Version Works:
1. Test basic functionality
2. Then try the full version (`telegram-dao-bot-switchboard.json`)
3. Add database credentials as needed

## Common Import Issues & Solutions

### Issue: "Could not find property option"
**Cause**: Node version mismatch or invalid property structure
**Solution**: Use the simple version first, then upgrade

### Issue: "Invalid expression"
**Cause**: Unsupported expression syntax
**Solution**: Replace `?.` with traditional conditionals:
```javascript
// ❌ May not work in older n8n versions
$json.message?.text

// ✅ Compatible version
$json.message ? $json.message.text : null
```

### Issue: "Unknown node type"
**Cause**: Missing node or outdated n8n version
**Solution**: Update n8n or use alternative nodes

### Issue: "Credential not found"
**Cause**: Referenced credentials don't exist
**Solution**: Create credentials first or update credential IDs

## Testing the Simple Workflow

Once imported successfully:

1. **Test /start command**:
   - Send `/start` to your bot
   - Should receive welcome message with roadmap button

2. **Test roadmap callback**:
   - Click the roadmap button
   - Should display roadmap information

3. **Test new member**:
   - Add the bot to a group
   - Add a new member
   - Should send welcome message

4. **Test admin commands**:
   - Send `/kick` or `/pin`
   - Should receive "Admin command received" message

## Expanding the Simple Workflow

Once the simple version works, you can:

1. **Add database nodes** for user management
2. **Add admin validation** for kick/pin commands
3. **Add investment tracking** features
4. **Add more complex routing** logic

## Version Compatibility Notes

The simple workflow uses:
- **Switch node v1**: Most compatible version
- **Set node v1**: Standard version
- **Telegram node v1.2**: Current stable version
- **Basic expressions**: No advanced syntax

This ensures maximum compatibility across n8n versions.

## Next Steps

1. **Import simple version** to test switchboard concept
2. **Verify basic functionality** works
3. **Add features incrementally** as needed
4. **Use as foundation** for more complex workflows

The simple version demonstrates the switchboard pattern without complex dependencies, making it easier to troubleshoot and extend.
