+{
  "name": "My workflow",
  "nodes": [
    {
      "parameters": {
        "updates": [
          "message",
          "callback_query",
          "pre_checkout_query",
          "successful_payment"
        ],
        "additionalFields": {}
      },
      "type": "n8n-nodes-base.telegramTrigger",
      "typeVersion": 1.2,
      "position": [
        -848,
        -176
      ],
      "id": "1019379d-3ae6-4ed3-a005-08b4ffef9e90",
      "name": "Telegram Trigger",
      "webhookId": "23b39a94-f325-4058-9e19-68f6cc044920",
      "credentials": {
        "telegramApi": {
          "id": "TJrFezJVWaryfEAe",
          "name": "Telegram account"
        }
      }
    },
    {
      "parameters": {
        "rules": {
          "values": [
            {
              "conditions": {
                "options": {
                  "caseSensitive": true,
                  "leftValue": "",
                  "typeValidation": "strict",
                  "version": 2
                },
                "conditions": [
                  {
                    "leftValue": "{{$json[\"message\"][\"text\"]}}",
                    "rightValue": "/start",
                    "operator": {
                      "type": "string",
                      "operation": "equals"
                    },
                    "id": "c4795199-7c79-4b5e-b91b-d77c3e6fc53d"
                  }
                ],
                "combinator": "and"
              },
              "renameOutput": true,
              "outputKey": "Welcome"
            },
            {
              "conditions": {
                "options": {
                  "caseSensitive": true,
                  "leftValue": "",
                  "typeValidation": "strict",
                  "version": 2
                },
                "conditions": [
                  {
                    "id": "62d8cb3b-43ab-4f99-80e4-6f1741b46d42",
                    "leftValue": "={{$json[\"message\"][\"text\"]}}",
                    "rightValue": "/vendors",
                    "operator": {
                      "type": "string",
                      "operation": "equals",
                      "name": "filter.operator.equals"
                    }
                  }
                ],
                "combinator": "and"
              },
              "renameOutput": true,
              "outputKey": "Vendors"
            },
            {
              "conditions": {
                "options": {
                  "caseSensitive": true,
                  "leftValue": "",
                  "typeValidation": "strict",
                  "version": 2
                },
                "conditions": [
                  {
                    "id": "callback-query-condition",
                    "leftValue": "={{$json[\"callback_query\"][\"data\"]}}",
                    "rightValue": "Vendors",
                    "operator": {
                      "type": "string",
                      "operation": "equals",
                      "name": "filter.operator.equals"
                    }
                  }
                ],
                "combinator": "and"
              },
              "renameOutput": true,
              "outputKey": "CallbackVendors"
            },
            {
              "conditions": {
                "options": {
                  "caseSensitive": true,
                  "leftValue": "",
                  "typeValidation": "strict",
                  "version": 2
                },
                "conditions": [
                  {
                    "id": "fa012ef9-5b99-4922-b134-fca073f90994",
                    "leftValue": "={{$json[\"message\"][\"text\"]}}",
                    "rightValue": "/menu",
                    "operator": {
                      "type": "string",
                      "operation": "equals",
                      "name": "filter.operator.equals"
                    }
                  }
                ],
                "combinator": "and"
              },
              "renameOutput": true,
              "outputKey": "Menu"
            },
            {
              "conditions": {
                "options": {
                  "caseSensitive": true,
                  "leftValue": "",
                  "typeValidation": "strict",
                  "version": 2
                },
                "conditions": [
                  {
                    "id": "27f37457-3e10-4eb8-a9cd-dbceef71f5ad",
                    "leftValue": "={{$json[\"message\"][\"text\"]}}",
                    "rightValue": "/order",
                    "operator": {
                      "type": "string",
                      "operation": "equals",
                      "name": "filter.operator.equals"
                    }
                  }
                ],
                "combinator": "and"
              },
              "renameOutput": true,
              "outputKey": "Order"
            },
            {
              "conditions": {
                "options": {
                  "caseSensitive": true,
                  "leftValue": "",
                  "typeValidation": "strict",
                  "version": 2
                },
                "conditions": [
                  {
                    "id": "callback-browse-menu",
                    "leftValue": "={{$json[\"callback_query\"][\"data\"]}}",
                    "rightValue": "browse_menu",
                    "operator": {
                      "type": "string",
                      "operation": "equals"
                    }
                  }
                ],
                "combinator": "and"
              },
              "renameOutput": true,
              "outputKey": "CallbackMenu"
            },
            {
              "conditions": {
                "options": {
                  "caseSensitive": true,
                  "leftValue": "",
                  "typeValidation": "strict",
                  "version": 2
                },
                "conditions": [
                  {
                    "id": "callback-place-order",
                    "leftValue": "={{$json[\"callback_query\"][\"data\"]}}",
                    "rightValue": "place_order",
                    "operator": {
                      "type": "string",
                      "operation": "equals"
                    }
                  }
                ],
                "combinator": "and"
              },
              "renameOutput": true,
              "outputKey": "CallbackOrder"
            },
            {
              "conditions": {
                "options": {
                  "caseSensitive": true,
                  "leftValue": "",
                  "typeValidation": "strict",
                  "version": 2
                },
                "conditions": [
                  {
                    "id": "callback-back-to-start",
                    "leftValue": "={{$json[\"callback_query\"][\"data\"]}}",
                    "rightValue": "back_to_start",
                    "operator": {
                      "type": "string",
                      "operation": "equals"
                    }
                  }
                ],
                "combinator": "and"
              },
              "renameOutput": true,
              "outputKey": "CallbackBackToStart"
            },
            {
              "conditions": {
                "options": {
                  "caseSensitive": true,
                  "leftValue": "",
                  "typeValidation": "strict",
                  "version": 2
                },
                "conditions": [
                  {
                    "id": "callback-back-to-vendors",
                    "leftValue": "={{$json[\"callback_query\"][\"data\"]}}",
                    "rightValue": "back_to_vendors",
                    "operator": {
                      "type": "string",
                      "operation": "equals"
                    }
                  }
                ],
                "combinator": "and"
              },
              "renameOutput": true,
              "outputKey": "CallbackBackToVendors"
            },
            {
              "conditions": {
                "options": {
                  "caseSensitive": true,
                  "leftValue": "",
                  "typeValidation": "strict",
                  "version": 2
                },
                "conditions": [
                  {
                    "id": "callback-vendor-mama-js",
                    "leftValue": "={{$json[\"callback_query\"][\"data\"]}}",
                    "rightValue": "vendor_mama_js",
                    "operator": {
                      "type": "string",
                      "operation": "equals"
                    }
                  }
                ],
                "combinator": "and"
              },
              "renameOutput": true,
              "outputKey": "VendorMamaJs"
            },
            {
              "conditions": {
                "options": {
                  "caseSensitive": true,
                  "leftValue": "",
                  "typeValidation": "strict",
                  "version": 2
                },
                "conditions": [
                  {
                    "id": "callback-vendor-island-bites",
                    "leftValue": "={{$json[\"callback_query\"][\"data\"]}}",
                    "rightValue": "vendor_island_bites",
                    "operator": {
                      "type": "string",
                      "operation": "equals"
                    }
                  }
                ],
                "combinator": "and"
              },
              "renameOutput": true,
              "outputKey": "VendorIslandBites"
            },
            {
              "conditions": {
                "options": {
                  "caseSensitive": true,
                  "leftValue": "",
                  "typeValidation": "strict",
                  "version": 2
                },
                "conditions": [
                  {
                    "id": "callback-vendor-street-grill",
                    "leftValue": "={{$json[\"callback_query\"][\"data\"]}}",
                    "rightValue": "vendor_street_grill",
                    "operator": {
                      "type": "string",
                      "operation": "equals"
                    }
                  }
                ],
                "combinator": "and"
              },
              "renameOutput": true,
              "outputKey": "VendorStreetGrill"
            },
            {
              "conditions": {
                "options": {
                  "caseSensitive": true,
                  "leftValue": "",
                  "typeValidation": "strict",
                  "version": 2
                },
                "conditions": [
                  {
                    "id": "callback-category-mains",
                    "leftValue": "={{$json[\"callback_query\"][\"data\"]}}",
                    "rightValue": "category_mains",
                    "operator": {
                      "type": "string",
                      "operation": "equals"
                    }
                  }
                ],
                "combinator": "and"
              },
              "renameOutput": true,
              "outputKey": "MainsMenu"
            },
            {
              "conditions": {
                "options": {
                  "caseSensitive": true,
                  "leftValue": "",
                  "typeValidation": "strict",
                  "version": 2
                },
                "conditions": [
                  {
                    "id": "callback-category-drinks",
                    "leftValue": "={{$json[\"callback_query\"][\"data\"]}}",
                    "rightValue": "category_drinks",
                    "operator": {
                      "type": "string",
                      "operation": "equals"
                    }
                  }
                ],
                "combinator": "and"
              },
              "renameOutput": true,
              "outputKey": "DrinksMenu"
            },
            {
              "conditions": {
                "options": {
                  "caseSensitive": true,
                  "leftValue": "",
                  "typeValidation": "strict",
                  "version": 2
                },
                "conditions": [
                  {
                    "id": "callback-category-desserts",
                    "leftValue": "={{$json[\"callback_query\"][\"data\"]}}",
                    "rightValue": "category_desserts",
                    "operator": {
                      "type": "string",
                      "operation": "equals"
                    }
                  }
                ],
                "combinator": "and"
              },
              "renameOutput": true,
              "outputKey": "DessertsMenu"
            },
            {
              "conditions": {
                "options": {
                  "caseSensitive": true,
                  "leftValue": "",
                  "typeValidation": "strict",
                  "version": 2
                },
                "conditions": [
                  {
                    "id": "callback-category-sides",
                    "leftValue": "={{$json[\"callback_query\"][\"data\"]}}",
                    "rightValue": "category_sides",
                    "operator": {
                      "type": "string",
                      "operation": "equals"
                    }
                  }
                ],
                "combinator": "and"
              },
              "renameOutput": true,
              "outputKey": "SidesMenu"
            },
            {
              "conditions": {
                "options": {
                  "caseSensitive": true,
                  "leftValue": "",
                  "typeValidation": "strict",
                  "version": 2
                },
                "conditions": [
                  {
                    "leftValue": "={{$json.callback_query.data.startsWith('add_')}}",
                    "rightValue": "true",
                    "operator": {
                      "type": "boolean",
                      "operation": "equals"
                    },
                    "id": "add-item-condition"
                  }
                ],
                "combinator": "and"
              },
              "renameOutput": true,
              "outputKey": "AddItemToCart"
            },
            {
              "conditions": {
                "options": {
                  "caseSensitive": true,
                  "leftValue": "",
                  "typeValidation": "strict",
                  "version": 2
                },
                "conditions": [
                  {
                    "leftValue": "={{$json.callback_query.data.startsWith('menu_')}}",
                    "rightValue": "true",
                    "operator": {
                      "type": "boolean",
                      "operation": "equals"
                    },
                    "id": "vendor-menu-condition"
                  }
                ],
                "combinator": "and"
              },
              "renameOutput": true,
              "outputKey": "VendorMenuSelected"
            },
            {
              "conditions": {
                "options": {
                  "caseSensitive": true,
                  "leftValue": "",
                  "typeValidation": "strict",
                  "version": 2
                },
                "conditions": [
                  {
                    "leftValue": "={{$json.callback_query.data.startsWith('order_')}}",
                    "rightValue": "true",
                    "operator": {
                      "type": "boolean",
                      "operation": "equals"
                    },
                    "id": "vendor-order-condition"
                  }
                ],
                "combinator": "and"
              },
              "renameOutput": true,
              "outputKey": "VendorOrderSelected"
            },
            {
              "conditions": {
                "options": {
                  "caseSensitive": true,
                  "leftValue": "",
                  "typeValidation": "strict",
                  "version": 2
                },
                "conditions": [
                  {
                    "leftValue": "={{$json.update_type}}",
                    "rightValue": "pre_checkout_query",
                    "operator": {
                      "type": "string",
                      "operation": "equals"
                    },
                    "id": "pre-checkout-condition"
                  }
                ],
                "combinator": "and"
              },
              "renameOutput": true,
              "outputKey": "PreCheckoutQuery"
            },
            {
              "conditions": {
                "options": {
                  "caseSensitive": true,
                  "leftValue": "",
                  "typeValidation": "strict",
                  "version": 2
                },
                "conditions": [
                  {
                    "leftValue": "={{$json.update_type}}",
                    "rightValue": "successful_payment",
                    "operator": {
                      "type": "string",
                      "operation": "equals"
                    },
                    "id": "successful-payment-condition"
                  }
                ],
                "combinator": "and"
              },
              "renameOutput": true,
              "outputKey": "SuccessfulPayment"
            },
            {
              "conditions": {
                "options": {
                  "caseSensitive": true,
                  "leftValue": "",
                  "typeValidation": "strict",
                  "version": 2
                },
                "conditions": [
                  {
                    "leftValue": "={{$json.callback_query.data}}",
                    "rightValue": "view_cart",
                    "operator": {
                      "type": "string",
                      "operation": "equals"
                    },
                    "id": "view-cart-condition"
                  }
                ],
                "combinator": "and"
              },
              "renameOutput": true,
              "outputKey": "ViewCart"
            },
            {
              "conditions": {
                "options": {
                  "caseSensitive": true,
                  "leftValue": "",
                  "typeValidation": "strict",
                  "version": 2
                },
                "conditions": [
                  {
                    "leftValue": "={{$json.callback_query.data}}",
                    "rightValue": "checkout",
                    "operator": {
                      "type": "string",
                      "operation": "equals"
                    },
                    "id": "checkout-condition"
                  }
                ],
                "combinator": "and"
              },
              "renameOutput": true,
              "outputKey": "Checkout"
            }
          ]
        },
        "options": {}
      },
      "type": "n8n-nodes-base.switch",
      "typeVersion": 3.2,
      "position": [
        -592,
        -208
      ],
      "id": "1125a93a-a8ba-4f5d-9cc8-e19118080ce1",
      "name": "Switch"
    },
    {
      "parameters": {
        "chatId": "={{ $json.message.from.id }}",
        "text": "Vendors",
        "replyMarkup": "inlineKeyboard",
        "inlineKeyboard": {
          "rows": [
            {
              "row": {
                "buttons": [
                  {
                    "text": "Mama J’s Kitchen",
                    "additionalFields": {
                      "callback_data": "vendor_mama_js"
                    }
                  },
                  {
                    "text": "Island Bites",
                    "additionalFields": {
                      "callback_data": "vendor_island_bites"
                    }
                  },
                  {
                    "text": "Street Grill",
                    "additionalFields": {
                      "callback_data": "vendor_street_grill"
                    }
                  },
                  {
                    "text": "Back",
                    "additionalFields": {
                      "callback_data": "back_to_start"
                    }
                  }
                ]
              }
            }
          ]
        },
        "additionalFields": {}
      },
      "type": "n8n-nodes-base.telegram",
      "typeVersion": 1.2,
      "position": [
        -176,
        -272
      ],
      "id": "ae9e4c64-7f0b-4bf9-ab80-197a51a0ad99",
      "name": "Send a text message1",
      "webhookId": "d5518a68-7f3f-4c88-ab87-3efc9adfbf1d",
      "credentials": {
        "telegramApi": {
          "id": "TJrFezJVWaryfEAe",
          "name": "Telegram account"
        }
      }
    },
    {
      "parameters": {
        "chatId": "={{ $json.message ? $json.message.from.id : $json.callback_query.from.id }}",
        "text": "menu – pick a category ",
        "replyMarkup": "inlineKeyboard",
        "inlineKeyboard": {
          "rows": [
            {
              "row": {
                "buttons": [
                  {
                    "text": "Mains",
                    "additionalFields": {
                      "callback_data": "category_mains"
                    }
                  },
                  {
                    "text": "Drinks",
                    "additionalFields": {
                      "callback_data": "category_drinks"
                    }
                  },
                  {
                    "text": "Desserts ",
                    "additionalFields": {
                      "callback_data": "category_desserts"
                    }
                  },
                  {
                    "text": "Sides",
                    "additionalFields": {
                      "callback_data": "category_sides"
                    }
                  },
                  {
                    "text": "Back to Vendors",
                    "additionalFields": {
                      "callback_data": "back_to_vendors"
                    }
                  }
                ]
              }
            }
          ]
        },
        "additionalFields": {}
      },
      "type": "n8n-nodes-base.telegram",
      "typeVersion": 1.2,
      "position": [
        -176,
        -144
      ],
      "id": "6ea198d1-c3fb-4bc9-ade6-f5eb19f528af",
      "name": "Send a text message2",
      "webhookId": "5f036f4b-18b1-4d98-89c4-4458bf8a05c1",
      "credentials": {
        "telegramApi": {
          "id": "TJrFezJVWaryfEAe",
          "name": "Telegram account"
        }
      }
    },
    {
      "parameters": {
        "chatId": "={{ $json.message ? $json.message.from.id : $json.callback_query.from.id }}",
        "text": "Order – confirm pickup or delivery",
        "replyMarkup": "inlineKeyboard",
        "inlineKeyboard": {
          "rows": [
            {
              "row": {
                "buttons": [
                  {
                    "text": "Confirm Pickup",
                    "additionalFields": {
                      "callback_data": "confirm_pickup"
                    }
                  },
                  {
                    "text": "Confirm Delivery",
                    "additionalFields": {
                      "callback_data": "confirm_delivery"
                    }
                  },
                  {
                    "text": "Cancel Order",
                    "additionalFields": {
                      "callback_data": "cancel_order"
                    }
                  }
                ]
              }
            }
          ]
        },
        "additionalFields": {}
      },
      "type": "n8n-nodes-base.telegram",
      "typeVersion": 1.2,
      "position": [
        -176,
        0
      ],
      "id": "888a93a0-2ae7-4d12-ad35-bf7a4ec56a78",
      "name": "Send a text message3",
      "webhookId": "276299c6-4d14-4a62-aa0e-7cd69f1c140f",
      "credentials": {
        "telegramApi": {
          "id": "TJrFezJVWaryfEAe",
          "name": "Telegram account"
        }
      }
    },
    {
      "parameters": {
        "chatId": "={{ $json.message.from.id }}",
        "text": "Choose an option below to get started.",
        "replyMarkup": "inlineKeyboard",
        "forceReply": {},
        "inlineKeyboard": {
          "rows": [
            {
              "row": {
                "buttons": [
                  {
                    "text": "View Vendors",
                    "additionalFields": {
                      "callback_data": "Vendors"
                    }
                  },
                  {
                    "text": "Browse Menu",
                    "additionalFields": {
                      "callback_data": "browse_menu"
                    }
                  },
                  {
                    "text": "Place Order",
                    "additionalFields": {
                      "callback_data": "place_order"
                    }
                  }
                ]
              }
            }
          ]
        },
        "replyKeyboardOptions": {},
        "replyKeyboardRemove": {},
        "additionalFields": {
          "appendAttribution": false
        }
      },
      "type": "n8n-nodes-base.telegram",
      "typeVersion": 1.2,
      "position": [
        -160,
        -432
      ],
      "id": "60998f35-b4d0-4615-a90f-39791781e7cb",
      "name": "Send a text message",
      "webhookId": "280904cc-4b9d-4078-be23-5bccfc3a962f",
      "credentials": {
        "telegramApi": {
          "id": "TJrFezJVWaryfEAe",
          "name": "Telegram account"
        }
      }
    },
    {
      "parameters": {
        "conditions": {
          "options": {
            "caseSensitive": true,
            "leftValue": "",
            "typeValidation": "strict"
          },
          "conditions": [
            {
              "id": "callback-exists-check",
              "leftValue": "={{ $json.callback_query }}",
              "rightValue": "",
              "operator": {
                "type": "object",
                "operation": "exists",
                "singleValue": true
              }
            }
          ],
          "combinator": "and"
        },
        "options": {}
      },
      "id": "callback-filter-if",
      "name": "Callback Query Filter",
      "type": "n8n-nodes-base.if",
      "typeVersion": 2,
      "position": [
        200,
        -368
      ]
    },
    {
      "parameters": {
        "resource": "callback",
        "queryId": "={{ $json.callback_query.id }}",
        "additionalFields": {}
      },
      "type": "n8n-nodes-base.telegram",
      "typeVersion": 1.2,
      "position": [
        384,
        -368
      ],
      "id": "afdbccfa-ca2c-41c9-8a3b-3bbbb41e3e74",
      "name": "Answer Query a callback",
      "webhookId": "1c00e020-a799-4f49-ba33-87ff8b2f3541",
      "credentials": {
        "telegramApi": {
          "id": "TJrFezJVWaryfEAe",
          "name": "Telegram account"
        }
      }
    },
    {
      "parameters": {
        "chatId": "={{ $json.message ? $json.message.from.id : $json.callback_query.from.id }}",
        "text": "🍽️ **Mama J's Kitchen**\n\nSpecializing in authentic Caribbean cuisine with a modern twist. Known for their jerk chicken and curry dishes.\n\n📍 Location: Downtown Food Court\n⏰ Hours: 11:00 AM - 9:00 PM\n⭐ Rating: 4.8/5",
        "replyMarkup": "inlineKeyboard",
        "inlineKeyboard": {
          "rows": [
            {
              "row": {
                "buttons": [
                  {
                    "text": "View Menu",
                    "additionalFields": {
                      "callback_data": "menu_mama_js"
                    }
                  },
                  {
                    "text": "Place Order",
                    "additionalFields": {
                      "callback_data": "order_mama_js"
                    }
                  }
                ]
              }
            },
            {
              "row": {
                "buttons": [
                  {
                    "text": "← Back to Vendors",
                    "additionalFields": {
                      "callback_data": "back_to_vendors"
                    }
                  }
                ]
              }
            }
          ]
        },
        "additionalFields": {}
      },
      "type": "n8n-nodes-base.telegram",
      "typeVersion": 1.2,
      "position": [
        200,
        -400
      ],
      "id": "vendor-mama-js-node",
      "name": "Mama J's Kitchen Details",
      "credentials": {
        "telegramApi": {
          "id": "TJrFezJVWaryfEAe",
          "name": "Telegram account"
        }
      }
    },
    {
      "parameters": {
        "chatId": "={{ $json.message ? $json.message.from.id : $json.callback_query.from.id }}",
        "text": "🏝️ **Island Bites**\n\nFresh seafood and tropical flavors from the Caribbean islands. Famous for their fish tacos and coconut shrimp.\n\n📍 Location: Beachside Plaza\n⏰ Hours: 12:00 PM - 10:00 PM\n⭐ Rating: 4.6/5",
        "replyMarkup": "inlineKeyboard",
        "inlineKeyboard": {
          "rows": [
            {
              "row": {
                "buttons": [
                  {
                    "text": "View Menu",
                    "additionalFields": {
                      "callback_data": "menu_island_bites"
                    }
                  },
                  {
                    "text": "Place Order",
                    "additionalFields": {
                      "callback_data": "order_island_bites"
                    }
                  }
                ]
              }
            },
            {
              "row": {
                "buttons": [
                  {
                    "text": "← Back to Vendors",
                    "additionalFields": {
                      "callback_data": "back_to_vendors"
                    }
                  }
                ]
              }
            }
          ]
        },
        "additionalFields": {}
      },
      "type": "n8n-nodes-base.telegram",
      "typeVersion": 1.2,
      "position": [
        200,
        -200
      ],
      "id": "vendor-island-bites-node",
      "name": "Island Bites Details",
      "credentials": {
        "telegramApi": {
          "id": "TJrFezJVWaryfEAe",
          "name": "Telegram account"
        }
      }
    },
    {
      "parameters": {
        "chatId": "={{ $json.message ? $json.message.from.id : $json.callback_query.from.id }}",
        "text": "🔥 **Street Grill**\n\nAuthentic street food with bold flavors. Specializing in grilled meats, plantains, and traditional sides.\n\n📍 Location: Market Street Corner\n⏰ Hours: 10:00 AM - 11:00 PM\n⭐ Rating: 4.7/5",
        "replyMarkup": "inlineKeyboard",
        "inlineKeyboard": {
          "rows": [
            {
              "row": {
                "buttons": [
                  {
                    "text": "View Menu",
                    "additionalFields": {
                      "callback_data": "menu_street_grill"
                    }
                  },
                  {
                    "text": "Place Order",
                    "additionalFields": {
                      "callback_data": "order_street_grill"
                    }
                  }
                ]
              }
            },
            {
              "row": {
                "buttons": [
                  {
                    "text": "← Back to Vendors",
                    "additionalFields": {
                      "callback_data": "back_to_vendors"
                    }
                  }
                ]
              }
            }
          ]
        },
        "additionalFields": {}
      },
      "type": "n8n-nodes-base.telegram",
      "typeVersion": 1.2,
      "position": [
        200,
        0
      ],
      "id": "vendor-street-grill-node",
      "name": "Street Grill Details",
      "credentials": {
        "telegramApi": {
          "id": "TJrFezJVWaryfEAe",
          "name": "Telegram account"
        }
      }
    },
    {
      "parameters": {
        "chatId": "={{ $json.message ? $json.message.from.id : $json.callback_query.from.id }}",
        "text": "🍽️ **MAINS MENU**\n\nOur signature main dishes featuring authentic Caribbean flavors:\n\n🔥 **Jerk Chicken** - $18.99\nSpicy marinated chicken grilled to perfection\n\n🍛 **Curry Goat** - $22.99\nTender goat meat in aromatic curry sauce\n\n🐟 **Escovitch Fish** - $19.99\nFried fish with pickled vegetables\n\n🍖 **Oxtail Stew** - $24.99\nSlow-cooked oxtail in rich gravy\n\n🌶️ **Jerk Pork** - $20.99\nSpicy grilled pork with island seasonings",
        "replyMarkup": "inlineKeyboard",
        "inlineKeyboard": {
          "rows": [
            {
              "row": {
                "buttons": [
                  {
                    "text": "🛒 Add Jerk Chicken",
                    "additionalFields": {
                      "callback_data": "add_jerk_chicken"
                    }
                  },
                  {
                    "text": "🛒 Add Curry Goat",
                    "additionalFields": {
                      "callback_data": "add_curry_goat"
                    }
                  }
                ]
              }
            },
            {
              "row": {
                "buttons": [
                  {
                    "text": "🛒 Add Escovitch Fish",
                    "additionalFields": {
                      "callback_data": "add_escovitch_fish"
                    }
                  },
                  {
                    "text": "🛒 Add Oxtail Stew",
                    "additionalFields": {
                      "callback_data": "add_oxtail_stew"
                    }
                  }
                ]
              }
            },
            {
              "row": {
                "buttons": [
                  {
                    "text": "🛒 Add Jerk Pork",
                    "additionalFields": {
                      "callback_data": "add_jerk_pork"
                    }
                  },
                  {
                    "text": "← Back to Categories",
                    "additionalFields": {
                      "callback_data": "browse_menu"
                    }
                  }
                ]
              }
            }
          ]
        },
        "additionalFields": {}
      },
      "type": "n8n-nodes-base.telegram",
      "typeVersion": 1.2,
      "position": [
        400,
        -400
      ],
      "id": "mains-menu-node",
      "name": "Mains Menu",
      "credentials": {
        "telegramApi": {
          "id": "TJrFezJVWaryfEAe",
          "name": "Telegram account"
        }
      }
    },
    {
      "parameters": {
        "chatId": "={{ $json.message ? $json.message.from.id : $json.callback_query.from.id }}",
        "text": "🥤 **DRINKS MENU**\n\nRefreshing beverages to complement your meal:\n\n🥭 **Mango Juice** - $4.99\nFresh tropical mango juice\n\n🥥 **Coconut Water** - $3.99\nNatural coconut water\n\n🍹 **Rum Punch** - $8.99\nTraditional Caribbean cocktail\n\n☕ **Blue Mountain Coffee** - $5.99\nPremium Jamaican coffee\n\n🧊 **Sorrel Drink** - $4.49\nSpiced hibiscus beverage\n\n💧 **Water** - $1.99\nBottled spring water",
        "replyMarkup": "inlineKeyboard",
        "inlineKeyboard": {
          "rows": [
            {
              "row": {
                "buttons": [
                  {
                    "text": "🛒 Add Mango Juice",
                    "additionalFields": {
                      "callback_data": "add_mango_juice"
                    }
                  },
                  {
                    "text": "🛒 Add Coconut Water",
                    "additionalFields": {
                      "callback_data": "add_coconut_water"
                    }
                  }
                ]
              }
            },
            {
              "row": {
                "buttons": [
                  {
                    "text": "🛒 Add Rum Punch",
                    "additionalFields": {
                      "callback_data": "add_rum_punch"
                    }
                  },
                  {
                    "text": "🛒 Add Coffee",
                    "additionalFields": {
                      "callback_data": "add_coffee"
                    }
                  }
                ]
              }
            },
            {
              "row": {
                "buttons": [
                  {
                    "text": "🛒 Add Sorrel Drink",
                    "additionalFields": {
                      "callback_data": "add_sorrel"
                    }
                  },
                  {
                    "text": "← Back to Categories",
                    "additionalFields": {
                      "callback_data": "browse_menu"
                    }
                  }
                ]
              }
            }
          ]
        },
        "additionalFields": {}
      },
      "type": "n8n-nodes-base.telegram",
      "typeVersion": 1.2,
      "position": [
        400,
        -200
      ],
      "id": "drinks-menu-node",
      "name": "Drinks Menu",
      "credentials": {
        "telegramApi": {
          "id": "TJrFezJVWaryfEAe",
          "name": "Telegram account"
        }
      }
    },
    {
      "parameters": {
        "chatId": "={{ $json.message ? $json.message.from.id : $json.callback_query.from.id }}",
        "text": "🍰 **DESSERTS MENU**\n\nSweet treats to end your meal perfectly:\n\n🥥 **Coconut Drops** - $6.99\nTraditional coconut candy\n\n🍌 **Banana Bread** - $5.99\nMoist homemade banana bread\n\n🧁 **Rum Cake** - $7.99\nRich cake soaked in Caribbean rum\n\n🍮 **Sweet Potato Pudding** - $6.49\nCreamy spiced pudding\n\n🥧 **Plantain Tart** - $6.99\nSweet plantain in pastry crust\n\n🍨 **Coconut Ice Cream** - $4.99\nHomemade tropical ice cream",
        "replyMarkup": "inlineKeyboard",
        "inlineKeyboard": {
          "rows": [
            {
              "row": {
                "buttons": [
                  {
                    "text": "🛒 Add Coconut Drops",
                    "additionalFields": {
                      "callback_data": "add_coconut_drops"
                    }
                  },
                  {
                    "text": "🛒 Add Banana Bread",
                    "additionalFields": {
                      "callback_data": "add_banana_bread"
                    }
                  }
                ]
              }
            },
            {
              "row": {
                "buttons": [
                  {
                    "text": "🛒 Add Rum Cake",
                    "additionalFields": {
                      "callback_data": "add_rum_cake"
                    }
                  },
                  {
                    "text": "🛒 Add Sweet Potato Pudding",
                    "additionalFields": {
                      "callback_data": "add_sweet_potato_pudding"
                    }
                  }
                ]
              }
            },
            {
              "row": {
                "buttons": [
                  {
                    "text": "🛒 Add Plantain Tart",
                    "additionalFields": {
                      "callback_data": "add_plantain_tart"
                    }
                  },
                  {
                    "text": "← Back to Categories",
                    "additionalFields": {
                      "callback_data": "browse_menu"
                    }
                  }
                ]
              }
            }
          ]
        },
        "additionalFields": {}
      },
      "type": "n8n-nodes-base.telegram",
      "typeVersion": 1.2,
      "position": [
        400,
        0
      ],
      "id": "desserts-menu-node",
      "name": "Desserts Menu",
      "credentials": {
        "telegramApi": {
          "id": "TJrFezJVWaryfEAe",
          "name": "Telegram account"
        }
      }
    },
    {
      "parameters": {
        "chatId": "={{ $json.message ? $json.message.from.id : $json.callback_query.from.id }}",
        "text": "🥗 **SIDES MENU**\n\nPerfect accompaniments to your main dish:\n\n🍚 **Rice & Peas** - $4.99\nCoconut rice with kidney beans\n\n🍌 **Fried Plantains** - $3.99\nSweet caramelized plantains\n\n🥬 **Steamed Vegetables** - $4.49\nSeasonal mixed vegetables\n\n🍞 **Festival** - $2.99\nSweet fried dumplings\n\n🥔 **Yam & Dumplings** - $5.49\nBoiled yam with flour dumplings\n\n🌶️ **Coleslaw** - $3.49\nFresh cabbage salad with spicy dressing",
        "replyMarkup": "inlineKeyboard",
        "inlineKeyboard": {
          "rows": [
            {
              "row": {
                "buttons": [
                  {
                    "text": "🛒 Add Rice & Peas",
                    "additionalFields": {
                      "callback_data": "add_rice_peas"
                    }
                  },
                  {
                    "text": "🛒 Add Fried Plantains",
                    "additionalFields": {
                      "callback_data": "add_plantains"
                    }
                  }
                ]
              }
            },
            {
              "row": {
                "buttons": [
                  {
                    "text": "🛒 Add Steamed Vegetables",
                    "additionalFields": {
                      "callback_data": "add_vegetables"
                    }
                  },
                  {
                    "text": "🛒 Add Festival",
                    "additionalFields": {
                      "callback_data": "add_festival"
                    }
                  }
                ]
              }
            },
            {
              "row": {
                "buttons": [
                  {
                    "text": "🛒 Add Yam & Dumplings",
                    "additionalFields": {
                      "callback_data": "add_yam_dumplings"
                    }
                  },
                  {
                    "text": "← Back to Categories",
                    "additionalFields": {
                      "callback_data": "browse_menu"
                    }
                  }
                ]
              }
            }
          ]
        },
        "additionalFields": {}
      },
      "type": "n8n-nodes-base.telegram",
      "typeVersion": 1.2,
      "position": [
        400,
        200
      ],
      "id": "sides-menu-node",
      "name": "Sides Menu",
      "credentials": {
        "telegramApi": {
          "id": "TJrFezJVWaryfEAe",
          "name": "Telegram account"
        }
      }
    },
    {
      "parameters": {
        "functionCode": "// Extract item name from callback data\nconst callbackData = $json.callback_query.data;\nconst itemName = callbackData.replace('add_', '').replace(/_/g, ' ');\n\n// Find the item from our static menu data\nconst menuItems = {\n  'jerk chicken': { name: 'Jerk Chicken', price: 18.99, category: 'mains' },\n  'curry goat': { name: 'Curry Goat', price: 22.99, category: 'mains' },\n  'escovitch fish': { name: 'Escovitch Fish', price: 19.99, category: 'mains' },\n  'oxtail stew': { name: 'Oxtail Stew', price: 24.99, category: 'mains' },\n  'jerk pork': { name: 'Jerk Pork', price: 20.99, category: 'mains' },\n  'mango juice': { name: 'Mango Juice', price: 4.99, category: 'drinks' },\n  'coconut water': { name: 'Coconut Water', price: 3.99, category: 'drinks' },\n  'rum punch': { name: 'Rum Punch', price: 8.99, category: 'drinks' },\n  'coffee': { name: 'Blue Mountain Coffee', price: 5.99, category: 'drinks' },\n  'sorrel': { name: 'Sorrel Drink', price: 4.49, category: 'drinks' },\n  'coconut drops': { name: 'Coconut Drops', price: 6.99, category: 'desserts' },\n  'banana bread': { name: 'Banana Bread', price: 5.99, category: 'desserts' },\n  'rum cake': { name: 'Rum Cake', price: 7.99, category: 'desserts' },\n  'sweet potato pudding': { name: 'Sweet Potato Pudding', price: 6.49, category: 'desserts' },\n  'plantain tart': { name: 'Plantain Tart', price: 6.99, category: 'desserts' },\n  'rice peas': { name: 'Rice & Peas', price: 4.99, category: 'sides' },\n  'plantains': { name: 'Fried Plantains', price: 3.99, category: 'sides' },\n  'vegetables': { name: 'Steamed Vegetables', price: 4.49, category: 'sides' },\n  'festival': { name: 'Festival', price: 2.99, category: 'sides' },\n  'yam dumplings': { name: 'Yam & Dumplings', price: 5.49, category: 'sides' }\n};\n\nconst item = menuItems[itemName];\nif (!item) {\n  return [{ json: { error: 'Item not found', itemName: itemName } }];\n}\n\nconst userId = $json.callback_query.from.id;\n\n// Get existing cart from global variable or initialize\nlet userCart = global.get(`cart_${userId}`) || { items: [], total: 0 };\n\n// Check if item already exists in cart\nlet existingItemIndex = userCart.items.findIndex(cartItem => cartItem.name === item.name);\n\nif (existingItemIndex > -1) {\n  // Increase quantity\n  userCart.items[existingItemIndex].quantity += 1;\n} else {\n  // Add new item\n  userCart.items.push({\n    name: item.name,\n    price: item.price,\n    quantity: 1,\n    category: item.category\n  });\n}\n\n// Recalculate total\nuserCart.total = userCart.items.reduce((sum, cartItem) => sum + (cartItem.price * cartItem.quantity), 0);\n\n// Save cart back to global variable\nglobal.set(`cart_${userId}`, userCart);\n\nreturn [{\n  json: {\n    success: true,\n    message: `${item.name} added to cart! 🛒`,\n    cart: userCart,\n    userId: userId\n  }\n}];"
      },
      "type": "n8n-nodes-base.function",
      "typeVersion": 1,
      "position": [
        600,
        -100
      ],
      "id": "add-item-to-cart-function",
      "name": "Add Item to Cart"
    },
    {
      "parameters": {
        "chatId": "={{ $json.callback_query.from.id }}",
        "text": "={{ $json.message }}\\n\\n🛒 **Your Cart:**\\n{{ $json.cart.items.map(item => `• ${item.name} x${item.quantity} - $${(item.price * item.quantity).toFixed(2)}`).join('\\n') }}\\n\\n💰 **Total: ${{ $json.cart.total.toFixed(2) }}**",
        "replyMarkup": "inlineKeyboard",
        "inlineKeyboard": {
          "rows": [
            {
              "row": {
                "buttons": [
                  {
                    "text": "🛒 View Full Cart",
                    "additionalFields": {
                      "callback_data": "view_cart"
                    }
                  },
                  {
                    "text": "💳 Checkout",
                    "additionalFields": {
                      "callback_data": "checkout"
                    }
                  }
                ]
              }
            },
            {
              "row": {
                "buttons": [
                  {
                    "text": "← Continue Shopping",
                    "additionalFields": {
                      "callback_data": "browse_menu"
                    }
                  }
                ]
              }
            }
          ]
        },
        "additionalFields": {}
      },
      "type": "n8n-nodes-base.telegram",
      "typeVersion": 1.2,
      "position": [
        800,
        -100
      ],
      "id": "send-cart-update",
      "name": "Send Cart Update",
      "credentials": {
        "telegramApi": {
          "id": "TJrFezJVWaryfEAe",
          "name": "Telegram account"
        }
      }
    },
    {
      "parameters": {
        "functionCode": "const userId = $json.callback_query.from.id;\nconst userCart = global.get(`cart_${userId}`) || { items: [], total: 0 };\n\nif (userCart.items.length === 0) {\n  return [{\n    json: {\n      isEmpty: true,\n      message: \"🛒 Your cart is empty!\\n\\nStart browsing our delicious Caribbean menu to add items.\"\n    }\n  }];\n}\n\nlet cartText = \"🛒 **Your Shopping Cart**\\n\\n\";\nuserCart.items.forEach((item, index) => {\n  cartText += `${index + 1}. **${item.name}**\\n`;\n  cartText += `   Quantity: ${item.quantity}\\n`;\n  cartText += `   Price: $${item.price.toFixed(2)} each\\n`;\n  cartText += `   Subtotal: $${(item.price * item.quantity).toFixed(2)}\\n\\n`;\n});\n\ncartText += `💰 **Total: $${userCart.total.toFixed(2)}**`;\n\nreturn [{\n  json: {\n    isEmpty: false,\n    cartText: cartText,\n    cart: userCart,\n    userId: userId\n  }\n}];"
      },
      "type": "n8n-nodes-base.function",
      "typeVersion": 1,
      "position": [
        600,
        100
      ],
      "id": "view-cart-function",
      "name": "View Cart Details"
    },
    {
      "parameters": {
        "chatId": "={{ $json.callback_query.from.id }}",
        "text": "={{ $json.cartText }}",
        "replyMarkup": "inlineKeyboard",
        "inlineKeyboard": {
          "rows": [
            {
              "row": {
                "buttons": [
                  {
                    "text": "💳 Proceed to Checkout",
                    "additionalFields": {
                      "callback_data": "checkout"
                    }
                  },
                  {
                    "text": "🗑️ Clear Cart",
                    "additionalFields": {
                      "callback_data": "clear_cart"
                    }
                  }
                ]
              }
            },
            {
              "row": {
                "buttons": [
                  {
                    "text": "← Continue Shopping",
                    "additionalFields": {
                      "callback_data": "browse_menu"
                    }
                  }
                ]
              }
            }
          ]
        },
        "additionalFields": {}
      },
      "type": "n8n-nodes-base.telegram",
      "typeVersion": 1.2,
      "position": [
        800,
        100
      ],
      "id": "send-cart-details",
      "name": "Send Cart Details",
      "credentials": {
        "telegramApi": {
          "id": "TJrFezJVWaryfEAe",
          "name": "Telegram account"
        }
      }
    },
    {
      "parameters": {
        "functionCode": "const userId = $json.callback_query.from.id;\nconst userCart = global.get(`cart_${userId}`) || { items: [], total: 0 };\n\nif (userCart.items.length === 0) {\n  return [{\n    json: {\n      error: true,\n      message: \"🛒 Your cart is empty! Add some items first.\"\n    }\n  }];\n}\n\n// Prepare checkout summary\nlet checkoutText = \"🧾 **Order Summary**\\n\\n\";\nuserCart.items.forEach((item, index) => {\n  checkoutText += `${index + 1}. ${item.name} x${item.quantity} - $${(item.price * item.quantity).toFixed(2)}\\n`;\n});\n\ncheckoutText += `\\n💰 **Total: $${userCart.total.toFixed(2)}**\\n\\n`;\ncheckoutText += \"Please choose your preferred payment method:\";\n\nreturn [{\n  json: {\n    error: false,\n    checkoutText: checkoutText,\n    cart: userCart,\n    userId: userId,\n    total: userCart.total\n  }\n}];"
      },
      "type": "n8n-nodes-base.function",
      "typeVersion": 1,
      "position": [
        600,
        300
      ],
      "id": "checkout-function",
      "name": "Prepare Checkout"
    },
    {
      "parameters": {
        "chatId": "={{ $json.callback_query.from.id }}",
        "text": "={{ $json.checkoutText }}",
        "replyMarkup": "inlineKeyboard",
        "inlineKeyboard": {
          "rows": [
            {
              "row": {
                "buttons": [
                  {
                    "text": "💳 Pay with Card",
                    "additionalFields": {
                      "callback_data": "pay_card"
                    }
                  },
                  {
                    "text": "💰 Cash on Delivery",
                    "additionalFields": {
                      "callback_data": "pay_cash"
                    }
                  }
                ]
              }
            },
            {
              "row": {
                "buttons": [
                  {
                    "text": "📍 Pickup",
                    "additionalFields": {
                      "callback_data": "pickup"
                    }
                  },
                  {
                    "text": "🚚 Delivery",
                    "additionalFields": {
                      "callback_data": "delivery"
                    }
                  }
                ]
              }
            },
            {
              "row": {
                "buttons": [
                  {
                    "text": "← Back to Cart",
                    "additionalFields": {
                      "callback_data": "view_cart"
                    }
                  }
                ]
              }
            }
          ]
        },
        "additionalFields": {}
      },
      "type": "n8n-nodes-base.telegram",
      "typeVersion": 1.2,
      "position": [
        800,
        300
      ],
      "id": "send-checkout-options",
      "name": "Send Checkout Options",
      "credentials": {
        "telegramApi": {
          "id": "TJrFezJVWaryfEAe",
          "name": "Telegram account"
        }
      }
    }
  ],
  "pinData": {},
  "connections": {
    "Telegram Trigger": {
      "main": [
        [
          {
            "node": "Switch",
            "type": "main",
            "index": 0
          },
          {
            "node": "Callback Query Filter",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Switch": {
      "main": [
        [
          {
            "node": "Send a text message",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Send a text message1",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Send a text message1",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Send a text message2",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Send a text message3",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Send a text message2",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Send a text message3",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Send a text message",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Send a text message1",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Mama J's Kitchen Details",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Island Bites Details",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Street Grill Details",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Mains Menu",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Drinks Menu",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Desserts Menu",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Sides Menu",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Add Item to Cart",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Mains Menu",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Send a text message3",
            "type": "main",
            "index": 0
          }
        ],
        [],
        [],
        [
          {
            "node": "View Cart Details",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Prepare Checkout",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Send a text message": {
      "main": [
        []
      ]
    },
    "Send a text message1": {
      "main": [
        []
      ]
    },
    "Send a text message2": {
      "main": [
        []
      ]
    },
    "Send a text message3": {
      "main": [
        []
      ]
    },
    "Mama J's Kitchen Details": {
      "main": [
        []
      ]
    },
    "Island Bites Details": {
      "main": [
        []
      ]
    },
    "Street Grill Details": {
      "main": [
        []
      ]
    },
    "Mains Menu": {
      "main": [
        []
      ]
    },
    "Drinks Menu": {
      "main": [
        []
      ]
    },
    "Desserts Menu": {
      "main": [
        []
      ]
    },
    "Sides Menu": {
      "main": [
        []
      ]
    },
    "Add Item to Cart": {
      "main": [
        [
          {
            "node": "Send Cart Update",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Send Cart Update": {
      "main": [
        []
      ]
    },
    "View Cart Details": {
      "main": [
        [
          {
            "node": "Send Cart Details",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Send Cart Details": {
      "main": [
        []
      ]
    },
    "Prepare Checkout": {
      "main": [
        [
          {
            "node": "Send Checkout Options",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Send Checkout Options": {
      "main": [
        []
      ]
    },
    "Callback Query Filter": {
      "main": [
        [
          {
            "node": "Answer Query a callback",
            "type": "main",
            "index": 0
          }
        ],
        []
      ]
    },
    "Answer Query a callback": {
      "main": [
        []
      ]
    }
  },
  "active": false,
  "settings": {
    "executionOrder": "v1"
  },
  "versionId": "8a2ab8ef-819e-4c75-8130-6f8ec80114bb",
  "meta": {
    "templateCredsSetupCompleted": true,
    "instanceId": "1f52f14222153875e5cd754160d07a91bc57a3dd7ee7266d4266eae0b531fc56"
  },
  "id": "QrP0bJgkM7BfiDj8",
  "tags": []
}