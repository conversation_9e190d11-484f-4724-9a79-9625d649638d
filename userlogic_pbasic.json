{"name": "My workflow 3", "nodes": [{"parameters": {"updates": ["message", "chat_member", "callback_query"], "additionalFields": {}}, "id": "6f89e99a-36d9-44b7-bda8-7c335c576acf", "name": "<PERSON>eg<PERSON>", "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.2, "position": [-1472, -144], "webhookId": "76bfb192-83ad-4541-9197-36d47cf7049b", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"values": {"string": [{"name": "botUsername", "value": "elohprocessingbot"}]}, "options": {}}, "id": "c4966a0f-c6c7-4eff-9825-febc5c6771c8", "name": "Set Variables", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [-1376, -144]}, {"parameters": {"dataType": "string", "value1": "={{ $json.message ? ($json.message.text.includes(' ') ? $json.message.text.split(' ')[0] : $json.message.text) : ($json.callback_query ? $json.callback_query.data : ($json.chat_member ? 'new_member' : '')) }}", "rules": {"rules": [{"value2": "/start"}, {"value2": "/kick", "output": 1}, {"value2": "/pin", "output": 1}, {"value2": "show_roadmap_public", "output": 2}, {"value2": "new_member", "output": 3}, {"value2": "/start view_investment", "output": 3}]}}, "id": "740926b8-52dd-4232-ba7f-e4a67990593f", "name": "Main Switch", "type": "n8n-nodes-base.switch", "typeVersion": 1, "position": [-1280, -160], "notes": "Routes all incoming events. Note that /kick and /pin go to the same output to be checked for admin rights first."}, {"parameters": {"operation": "get", "tableId": "project_roadmap", "filters": {"conditions": [{"keyName": "id"}]}}, "id": "c777d33a-0163-4527-a2a1-7cf92c31a980", "name": "Get Roadmap Data", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-1008, -32], "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"jsCode": "const items = $input.all();\nlet formattedText = '🗺️ **ELOH Processing Public Roadmap**\\n\\n';\nfor (const item of items) {\n  formattedText += `- **${item.json.name}**: ${item.json.status}\\n`;\n}\n\n$input.first().json.formatted_text = formattedText;\nreturn $input.first();"}, "id": "82aa6a7d-1d10-4d7f-bfac-99ac26f7e7c3", "name": "Format Roadmap Text", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-816, -32]}, {"parameters": {"chatId": "={{ $json.callback_query.message.chat.id }}", "text": "={{ $json.formatted_text }}", "additionalFields": {}}, "id": "73cb6ff2-ccf4-4f7d-a87d-00b85cbc1aa1", "name": "Send Roadmap", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-624, -32], "webhookId": "65af357d-c0e8-4dc8-aa35-8f91762784a9", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"chatId": "={{ $json.chat_member.chat.id }}", "text": "=Welcome @{{ $json.chat_member.new_chat_member.user.username }} to ELOH Processing DAO! 🎉\n\nPlease review our rules and start a private chat for investor features.", "replyMarkup": "inlineKeyboard", "inlineKeyboard": {"rows": [{"row": {"buttons": [{"text": "💬 Start Private Chat", "additionalFields": {"url": "={{ 'https://t.me/' + $node['Set Variables'].json.botUsername }}"}}]}}]}, "additionalFields": {}}, "id": "a692b5e5-1442-4e6c-b7db-7ddc921942a5", "name": "Send Welcome", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-768, 144], "webhookId": "9ae15ef7-31a5-4b20-a299-a7dbbdb3b941", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"operation": "get", "tableId": "users", "filters": {"conditions": [{"keyName": "telegram_id"}]}}, "id": "********-89c2-4a65-b29a-f01e29e0684f", "name": "Check User in Database", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-1008, -224], "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"conditions": {"options": {}, "conditions": [{"leftValue": "={{ $node[\"Check User in Database\"].json[0]?.is_verified_investor }}", "rightValue": true, "operation": {"type": "boolean", "operation": "true"}}]}, "options": {}}, "id": "8aa01e94-a179-410a-8aaa-adae175594fd", "name": "Check if Verified Investor", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-832, -416]}, {"parameters": {"chatId": "={{ $json.message.chat.id }}", "text": "=Hello {{ $node[\"Check User in Database\"].json[0].name }}! Your current investment value is: ${{ $node[\"Check User in Database\"].json[0].investment_details.total_value_usd }}.", "additionalFields": {}}, "id": "5e32643e-66a0-4a32-88c9-bfedcff5b529", "name": "Send Investment Details", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-400, -576], "webhookId": "2a151dcc-fc11-477a-accf-388dd945cf76", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"chatId": "={{ $json.message.chat.id }}", "text": "Your Telegram account is not yet linked to a verified investor profile. Please contact support.", "additionalFields": {}}, "id": "2268e8e8-aaf3-4c69-90ac-30db9546dcf7", "name": "Send Not Verified Message", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-464, -368], "webhookId": "ab0a81e9-8f5d-4df6-a6a9-958654f2ff15", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"operation": "get", "tableId": "v_users_admin_check", "filters": {"conditions": [{"keyName": "telegram_id"}]}}, "id": "1f8ebdb3-2d8f-47e6-8263-ebcb3cf6fa7f", "name": "Check if User is Admin", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-768, -208], "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"conditions": {"options": {}, "conditions": [{"leftValue": "={{$node['Check if User is Admin'].json.length}}", "rightValue": 0, "operation": {"type": "number", "operation": "larger"}}]}, "options": {}}, "id": "594c56c4-2fd6-46c7-a7e6-593db56157a1", "name": "IF Admin", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-592, -208]}, {"parameters": {"chatId": "={{ $json.message.chat.id }}", "text": "Admin command received. This feature is coming soon!", "additionalFields": {}}, "id": "4c236759-6d20-407a-9e1f-96641b1329d6", "name": "Admin Action Placeholder", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-320, -224], "webhookId": "5b9eb8ba-07ba-49b3-9f01-81b6def74252", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"chatId": "={{ $json.message.chat.id }}", "text": "Welcome to ELOH Processing DAO! 🚀", "replyMarkup": "inlineKeyboard", "inlineKeyboard": {"rows": [{"row": {"buttons": [{"text": "🗺️ Public Roadmap", "additionalFields": {"callback_data": "show_roadmap_public"}}]}}, {"row": {"buttons": [{"text": "💰 View My Investment", "additionalFields": {"url": "https://t.me/elohprocessingbot?start=view_investment"}}]}}]}, "additionalFields": {}}, "id": "db4f1791-ca21-4a54-bf3e-3d8a0c6c5ca2", "name": "Send Start Message", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-1040, 144], "webhookId": "63e9fd13-fae6-4adf-8571-cbc80e56671e", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}], "pinData": {}, "connections": {"Telegram Trigger": {"main": [[{"node": "Set Variables", "type": "main", "index": 0}]]}, "Set Variables": {"main": [[{"node": "Main Switch", "type": "main", "index": 0}]]}, "Main Switch": {"main": [[{"node": "Send Start Message", "type": "main", "index": 0}], [{"node": "Check User in Database", "type": "main", "index": 0}], [{"node": "Get Roadmap Data", "type": "main", "index": 0}], [{"node": "Send Start Message", "type": "main", "index": 0}]]}, "Get Roadmap Data": {"main": [[{"node": "Format Roadmap Text", "type": "main", "index": 0}]]}, "Format Roadmap Text": {"main": [[{"node": "Send Roadmap", "type": "main", "index": 0}]]}, "Check User in Database": {"main": [[{"node": "Check if Verified Investor", "type": "main", "index": 0}, {"node": "Check if User is Admin", "type": "main", "index": 0}]]}, "Check if Verified Investor": {"main": [[{"node": "Send Investment Details", "type": "main", "index": 0}], [{"node": "Send Not Verified Message", "type": "main", "index": 0}]]}, "Check if User is Admin": {"main": [[{"node": "IF Admin", "type": "main", "index": 0}]]}, "IF Admin": {"main": [[{"node": "Admin Action Placeholder", "type": "main", "index": 0}]]}, "Send Welcome": {"main": [[]]}, "Send Start Message": {"main": [[{"node": "Send Welcome", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "5a880cc1-df7a-4793-a6e4-9fcc023ab1f0", "meta": {"templateCredsSetupCompleted": true, "instanceId": "1f52f14222153875e5cd754160d07a91bc57a3dd7ee7266d4266eae0b531fc56"}, "id": "cl97drsgNofoPdxU", "tags": [{"createdAt": "2025-08-06T12:34:09.665Z", "updatedAt": "2025-08-06T12:34:09.665Z", "id": "EKr6mdBmEMIyCP37", "name": "ELOH"}]}