{"name": "Telegram DAO Bot - Full Corrected Workflow", "nodes": [{"parameters": {"updates": ["message", "chat_member"]}, "id": "telegramTriggerGroupEvents", "name": "<PERSON><PERSON><PERSON> (Group Events)", "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.1, "position": [240, 500], "credentials": {"telegramApi": {"id": "YOUR_TELEGRAM_CREDENTIALS_ID", "name": "Telegram Credentials"}}}, {"parameters": {"values": {"string": [{"name": "botUsername", "value": "YOUR_ACTUAL_BOT_USERNAME_HERE"}]}, "options": {}}, "id": "SetWorkflowVariables", "name": "Set Workflow Variables", "type": "n8n-nodes-base.set", "typeVersion": 1.1, "position": [460, 500]}, {"parameters": {"conditions": {"options": {}, "conditions": [{"leftValue": "={{$json.chat_member}}", "operation": {"type": "boolean", "operation": "exists"}}, {"leftValue": "={{$json.message.text}}", "rightValue": "/", "operation": {"type": "string", "operation": "startsWith"}}]}, "options": {}}, "id": "routeEventType", "name": "Route Event Type", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [680, 500]}, {"parameters": {"chatId": "={{$json.chat_member.chat.id}}", "text": "=Welcome @{{$json.chat_member.new_chat_member.user.username}} to the DAO! Please review our rules. To access investor features, please start a private chat with me.", "replyMarkup": "inlineKeyboard", "inlineKeyboard": {"rows": [{"row": {"buttons": [{"text": "Start Private Chat", "additionalFields": {"url": "={{ 'https://t.me/' + $node[\"SetWorkflowVariables\"].json.botUsername }}"}}]}}]}}, "id": "sendWelcomeMessage", "name": "Send Welcome Message", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [900, 400], "credentials": {"telegramApi": {"id": "YOUR_TELEGRAM_CREDENTIALS_ID", "name": "Telegram Credentials"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT role FROM public.users WHERE telegram_id = '{{$json.message.from.id}}' AND role = 'admin' LIMIT 1;", "options": {}}, "id": "checkIfUserIsAdmin", "name": "Check if User is Admin", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [900, 500], "credentials": {"supabase": {"id": "YOUR_SUPABASE_CREDENTIALS_ID", "name": "Supabase Credentials"}}}, {"parameters": {"conditions": {"options": {}, "conditions": [{"leftValue": "={{$node['checkIfUserIsAdmin'].json.length}}", "rightValue": 0, "operation": {"type": "number", "operation": "larger"}}]}, "options": {}}, "id": "IFAdmin", "name": "IF Admin", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1120, 500]}, {"parameters": {"conditions": {"options": {}, "conditions": [{"leftValue": "={{$json.message.text}}", "rightValue": "/kick", "operation": {"type": "string", "operation": "startsWith"}}, {"leftValue": "={{$json.message.text}}", "rightValue": "/pin", "operation": {"type": "string", "operation": "startsWith"}}]}, "options": {}}, "id": "routeAdminCommand", "name": "Route Admin Command", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1340, 500]}, {"parameters": {"operation": "kickChatMember", "chatId": "={{$json.message.chat.id}}", "userId": "={{$json.message.reply_to_message.from.id}}", "options": {}}, "id": "kickUser", "name": "Kick User", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [1560, 400], "credentials": {"telegramApi": {"id": "YOUR_TELEGRAM_CREDENTIALS_ID", "name": "Telegram Credentials"}}}, {"parameters": {"operation": "pinChatMessage", "chatId": "={{$json.message.chat.id}}", "messageId": "={{$json.message.reply_to_message.message_id}}", "options": {"disableNotification": false}}, "id": "pinMessage", "name": "Pin Message", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [1560, 600], "credentials": {"telegramApi": {"id": "YOUR_TELEGRAM_CREDENTIALS_ID", "name": "Telegram Credentials"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT investments_topic_id, roadmap_topic_id FROM public.group_config LIMIT 1;", "options": {}}, "id": "GetTopicConfig", "name": "Get Topic Config", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [900, 600], "credentials": {"supabase": {"id": "YOUR_SUPABASE_CREDENTIALS_ID", "name": "Supabase Credentials"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true}, "conditions": [{"leftValue": "={{ $json.message.message_thread_id }}", "rightValue": "={{ $node[\"GetTopicConfig\"].json[0].investments_topic_id }}", "operation": {"type": "string", "operation": "equals"}}, {"leftValue": "={{ $json.message.message_thread_id }}", "rightValue": "={{ $node[\"GetTopicConfig\"].json[0].roadmap_topic_id }}", "operation": {"type": "string", "operation": "equals"}}]}, "options": {}}, "id": "routeByTopicId", "name": "Route by Topic ID", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1120, 600]}, {"parameters": {"chatId": "={{ $json.message.chat.id }}", "text": "This is the **Investments** topic. For personal details, please use the private link.", "replyMarkup": "inlineKeyboard", "inlineKeyboard": {"rows": [{"row": {"buttons": [{"text": "💰 View My Investment (Private)", "additionalFields": {"url": "={{ 'https://t.me/' + $node[\"SetWorkflowVariables\"].json.botUsername + '?start=view_investment' }}"}}]}}]}}, "id": "sendInvestmentTopicMenu", "name": "Send Investment Topic Menu", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [1340, 700], "credentials": {"telegramApi": {"id": "YOUR_TELEGRAM_CREDENTIALS_ID", "name": "Telegram Credentials"}}}, {"parameters": {"chatId": "={{ $json.message.chat.id }}", "text": "This is the **Roadmap** topic.", "replyMarkup": "inlineKeyboard", "inlineKeyboard": {"rows": [{"row": {"buttons": [{"text": "🗺️ Show Full Roadmap", "additionalFields": {"callbackData": "show_roadmap_public"}}]}}]}}, "id": "sendRoadmapTopicMenu", "name": "Send Roadmap Topic Menu", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [1340, 820], "credentials": {"telegramApi": {"id": "YOUR_TELEGRAM_CREDENTIALS_ID", "name": "Telegram Credentials"}}}, {"parameters": {"chatId": "={{ $json.message.chat.id }}", "text": "This is a general discussion topic. How can I assist?", "options": {}}, "id": "sendGeneralTopicMenu", "name": "Send General <PERSON><PERSON>", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [1340, 940], "credentials": {"telegramApi": {"id": "YOUR_TELEGRAM_CREDENTIALS_ID", "name": "Telegram Credentials"}}}, {"parameters": {"updates": ["message"]}, "id": "telegramTriggerPrivateChat", "name": "<PERSON><PERSON><PERSON> (Private Chat)", "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.1, "position": [240, 1100], "credentials": {"telegramApi": {"id": "YOUR_TELEGRAM_CREDENTIALS_ID", "name": "Telegram Credentials"}}}, {"parameters": {"conditions": {"options": {}, "conditions": [{"leftValue": "={{ $json.message.text }}", "rightValue": "/start view_investment", "operation": {"type": "string", "operation": "contains"}}]}, "options": {}}, "id": "routeByStartPayload", "name": "Route by Start Payload", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [460, 1100]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT * FROM public.users WHERE telegram_id = '{{ $json.message.from.id }}' LIMIT 1", "options": {}}, "id": "checkUserInDatabase", "name": "Check User in Database", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [680, 1020], "credentials": {"supabase": {"id": "YOUR_SUPABASE_CREDENTIALS_ID", "name": "Supabase Credentials"}}}, {"parameters": {"conditions": {"options": {}, "conditions": [{"leftValue": "={{ $node[\"checkUserInDatabase\"].json[0]?.is_verified_investor }}", "rightValue": true, "operation": {"type": "boolean", "operation": "true"}}]}, "options": {}}, "id": "checkIfVerifiedInvestor", "name": "Check if Verified Investor", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [900, 1020]}, {"parameters": {"chatId": "={{ $json.message.chat.id }}", "text": "=Hello {{ $node[\"checkUserInDatabase\"].json[0].name }}! Your current investment value is: ${{ $node[\"checkUserInDatabase\"].json[0].investment_details.total_value_usd }}."}, "id": "sendInvestmentDetails", "name": "Send Investment Details", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [1120, 920], "credentials": {"telegramApi": {"id": "YOUR_TELEGRAM_CREDENTIALS_ID", "name": "Telegram Credentials"}}}, {"parameters": {"chatId": "={{ $json.message.chat.id }}", "text": "Your Telegram account is not yet linked to a verified investor profile. Please contact support."}, "id": "sendNotVerifiedMessage", "name": "Send Not Verified Message", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [1120, 1120], "credentials": {"telegramApi": {"id": "YOUR_TELEGRAM_CREDENTIALS_ID", "name": "Telegram Credentials"}}}, {"parameters": {"updates": ["callback_query"]}, "id": "telegramTriggerCallbackQuery", "name": "<PERSON><PERSON><PERSON> (Callback Query)", "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.1, "position": [240, 1300], "credentials": {"telegramApi": {"id": "YOUR_TELEGRAM_CREDENTIALS_ID", "name": "Telegram Credentials"}}}, {"parameters": {"conditions": {"options": {}, "conditions": [{"leftValue": "={{ $json.callback_query.data }}", "rightValue": "show_roadmap_public", "operation": {"type": "string", "operation": "equals"}}]}, "options": {}}, "id": "routeByCallbackData", "name": "Route by Callback Data", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [460, 1300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT * FROM public.project_roadmap ORDER BY id;", "options": {}}, "id": "getRoadmapData", "name": "Get Roadmap Data", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [680, 1300], "credentials": {"supabase": {"id": "YOUR_SUPABASE_CREDENTIALS_ID", "name": "Supabase Credentials"}}}, {"parameters": {"jsCode": "const roadmapItems = $input.all();\nlet formattedText = '🗺️ **ELOH Processing Public Roadmap**\\n\\n';\nfor (const item of roadmapItems) {\n  formattedText += `- **${item.json.name}**: ${item.json.status}\\n`;\n}\nreturn { formatted_text: formattedText };"}, "id": "formatRoadmapText", "name": "Format Roadmap Text", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 1300]}, {"parameters": {"chatId": "={{ $node[\"routeByCallbackData\"].json.callback_query.message.chat.id }}", "text": "={{ $node[\"formatRoadmapText\"].json.formatted_text }}"}, "id": "showFullRoadmap", "name": "Show Full Roadmap", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [1120, 1300], "credentials": {"telegramApi": {"id": "YOUR_TELEGRAM_CREDENTIALS_ID", "name": "Telegram Credentials"}}}], "connections": {"telegramTriggerGroupEvents": {"main": [[{"node": "SetWorkflowVariables", "type": "main", "index": 0}]]}, "SetWorkflowVariables": {"main": [[{"node": "routeEventType", "type": "main", "index": 0}]]}, "routeEventType": {"main": [[{"node": "sendWelcomeMessage", "type": "main", "index": 0}], [{"node": "checkIfUserIsAdmin", "type": "main", "index": 0}], [{"node": "GetTopicConfig", "type": "main", "index": 0}]]}, "checkIfUserIsAdmin": {"main": [[{"node": "IFAdmin", "type": "main", "index": 0}]]}, "IFAdmin": {"main": [[{"node": "routeAdminCommand", "type": "main", "index": 0}], []]}, "routeAdminCommand": {"main": [[{"node": "kickUser", "type": "main", "index": 0}], [{"node": "pinMessage", "type": "main", "index": 0}], []]}, "GetTopicConfig": {"main": [[{"node": "routeByTopicId", "type": "main", "index": 0}]]}, "routeByTopicId": {"main": [[{"node": "sendInvestmentTopicMenu", "type": "main", "index": 0}], [{"node": "sendRoadmapTopicMenu", "type": "main", "index": 0}], [{"node": "sendGeneralTopicMenu", "type": "main", "index": 0}]]}, "telegramTriggerPrivateChat": {"main": [[{"node": "routeByStartPayload", "type": "main", "index": 0}]]}, "routeByStartPayload": {"main": [[{"node": "checkUserInDatabase", "type": "main", "index": 0}], []]}, "checkUserInDatabase": {"main": [[{"node": "checkIfVerifiedInvestor", "type": "main", "index": 0}]]}, "checkIfVerifiedInvestor": {"main": [[{"node": "sendInvestmentDetails", "type": "main", "index": 0}], [{"node": "sendNotVerifiedMessage", "type": "main", "index": 0}]]}, "telegramTriggerCallbackQuery": {"main": [[{"node": "routeByCallbackData", "type": "main", "index": 0}]]}, "routeByCallbackData": {"main": [[{"node": "getRoadmapData", "type": "main", "index": 0}], []]}, "getRoadmapData": {"main": [[{"node": "formatRoadmapText", "type": "main", "index": 0}]]}, "formatRoadmapText": {"main": [[{"node": "showFullRoadmap", "type": "main", "index": 0}]]}}}