{"name": "Merged Dynamic Workflow", "nodes": [{"parameters": {"updates": ["message", "callback_query", "pre_checkout_query", "successful_payment"], "additionalFields": {}}, "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.2, "position": [-848, -176], "id": "1019379d-3ae6-4ed3-a005-08b4ffef9e90", "name": "<PERSON>eg<PERSON>", "webhookId": "23b39a94-f325-4058-9e19-68f6cc044920", "credentials": {"telegramApi": {"id": "TJrFezJVWaryfEAe", "name": "Telegram account"}}}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "{{$json[\"message\"][\"text\"]}}", "rightValue": "/start", "operator": {"type": "string", "operation": "equals"}, "id": "c4795199-7c79-4b5e-b91b-d77c3e6fc53d"}], "combinator": "and"}, "renameOutput": true, "outputKey": "Welcome"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{$json.callback_query.data}}", "rightValue": "browse_vendors", "operator": {"type": "string", "operation": "equals"}, "id": "2d12e617-640a-4841-9457-37a5f6e8c75e"}], "combinator": "and"}, "renameOutput": true, "outputKey": "BrowseV<PERSON><PERSON>"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{$json.callback_query.data}}", "rightValue": "back_to_vendors", "operator": {"type": "string", "operation": "equals"}, "id": "402e1c9e-64d5-4927-a006-25816b67e1a7"}], "combinator": "and"}, "renameOutput": true, "outputKey": "BackToVendors"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{$json.callback_query.data.startsWith('vendor_')}}", "rightValue": "true", "operator": {"type": "boolean", "operation": "equals"}, "id": "d0407a51-5100-47e0-a929-23136a5a9359"}], "combinator": "and"}, "renameOutput": true, "outputKey": "VendorSelected"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{$json.callback_query.data.startsWith('menu_')}}", "rightValue": "true", "operator": {"type": "boolean", "operation": "equals"}, "id": "3b680798-251f-495f-a39c-51368942b036"}], "combinator": "and"}, "renameOutput": true, "outputKey": "MenuSelected"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{$json.callback_query.data.startsWith('item_')}}", "rightValue": "true", "operator": {"type": "boolean", "operation": "equals"}, "id": "936b801f-b51f-431e-b873-102a0172e811"}], "combinator": "and"}, "renameOutput": true, "outputKey": "ItemSelected"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{$json.update_type}}", "rightValue": "pre_checkout_query", "operator": {"type": "string", "operation": "equals"}, "id": "e24d2091-6330-4e3a-9774-8b049646b9a2"}], "combinator": "and"}, "renameOutput": true, "outputKey": "PreCheckoutQuery"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{$json.update_type}}", "rightValue": "successful_payment", "operator": {"type": "string", "operation": "equals"}, "id": "673f324d-8b83-4a6c-b3a1-7c98031a26d2"}], "combinator": "and"}, "renameOutput": true, "outputKey": "SuccessfulPayment"}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [-592, -208], "id": "1125a93a-a8ba-4f5d-9cc8-e19118080ce1", "name": "Main Switch"}, {"parameters": {"chatId": "={{ $json.message.from.id }}", "text": "Choose an option below to get started.", "replyMarkup": "inlineKeyboard", "forceReply": {}, "inlineKeyboard": {"rows": [{"row": {"buttons": [{"text": "View Vendors", "additionalFields": {"callback_data": "browse_vendors"}}]}}]}, "replyKeyboardOptions": {}, "replyKeyboardRemove": {}, "additionalFields": {"appendAttribution": false}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-160, -432], "id": "welcome-message-node", "name": "Welcome Message", "credentials": {"telegramApi": {"id": "TJrFezJVWaryfEAe", "name": "Telegram account"}}}, {"parameters": {"resource": "table", "operation": "read", "tableName": "vendors", "returnAll": true, "additionalFields": {}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-200, -240], "id": "get-vendors-from-db", "name": "Get Vendors", "credentials": {"supabaseApi": {"id": "TJrFezJVWaryfEAe", "name": "Supabase Connection"}}}, {"parameters": {"chatId": "={{ $json.callback_query.message.chat.id }}", "text": "Please select a vendor:", "replyMarkup": "inlineKeyboard", "forceReply": {}, "inlineKeyboard": {"rows": [{"row": {"buttons": [{"text": "={{ $json.name }}", "additionalFields": {"callback_data": "={{ 'vendor_' + $json.id }}"}}]}}, {"row": {"buttons": [{"text": "Back to Start", "additionalFields": {"callback_data": "back_to_start"}}]}}]}, "replyKeyboardOptions": {}, "replyKeyboardRemove": {}, "additionalFields": {"appendAttribution": false}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [120, -240], "id": "show-vendors-message", "name": "Show Vendors", "credentials": {"telegramApi": {"id": "TJrFezJVWaryfEAe", "name": "Telegram account"}}}, {"parameters": {"resource": "table", "operation": "read", "tableName": "menus", "additionalFields": {"filters": [{"column": "vendor_id", "operator": "equals", "value": "={{ $json.callback_query.data.split('_')[1] }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-200, -40], "id": "get-menus-from-db", "name": "Get Menus", "credentials": {"supabaseApi": {"id": "TJrFezJVWaryfEAe", "name": "Supabase Connection"}}}, {"parameters": {"chatId": "={{ $json.callback_query.message.chat.id }}", "text": "Select a menu:", "replyMarkup": "inlineKeyboard", "forceReply": {}, "inlineKeyboard": {"rows": [{"row": {"buttons": [{"text": "={{ $json.name }}", "additionalFields": {"callback_data": "={{ 'menu_' + $json.id }}"}}]}}, {"row": {"buttons": [{"text": "Back to Vendors", "additionalFields": {"callback_data": "back_to_vendors"}}]}}]}, "replyKeyboardOptions": {}, "replyKeyboardRemove": {}, "additionalFields": {"appendAttribution": false}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [120, -40], "id": "show-menus-message", "name": "Show Menus", "credentials": {"telegramApi": {"id": "TJrFezJVWaryfEAe", "name": "Telegram account"}}}], "connections": {"Telegram Trigger": {"main": [[{"node": "Main Switch", "type": "main", "index": 0}]]}, "Main Switch": {"main": [[{"node": "Welcome Message", "type": "main", "index": 0}], [{"node": "Get Vendors", "type": "main", "index": 0}], [], [{"node": "Get Menus", "type": "main", "index": 0}], [], [], [], [], [], [], [], [], [], [{"node": "Get Menus", "type": "main", "index": 0}]]}, "Welcome Message": {"main": [[]]}, "Get Vendors": {"main": [[{"node": "Show Vendors", "type": "main", "index": 0}]]}, "Show Vendors": {"main": [[]]}, "Get Menus": {"main": [[{"node": "Show Menus", "type": "main", "index": 0}]]}, "Show Menus": {"main": [[]]}}}