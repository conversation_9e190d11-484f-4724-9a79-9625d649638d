{"name": "My workflow 3 - Corrected Menu", "nodes": [{"parameters": {"updates": ["message", "chat_member", "callback_query"], "additionalFields": {}}, "id": "41256a75-ad13-4623-8cf9-04b48c9ee37f", "name": "<PERSON>eg<PERSON>", "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.2, "position": [-1344, 32], "webhookId": "76bfb192-83ad-4541-9197-36d47cf7049b", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"values": {"string": [{"name": "botUsername", "value": "elohprocessingbot"}]}, "options": {}}, "id": "60b6051a-ee5c-492a-8a9e-f0062017932b", "name": "Set Variables", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [-1140, 32]}, {"parameters": {"dataType": "string", "value1": "={{ $json.message ? ($json.message.text.includes(' ') ? $json.message.text.split(' ')[0] : $json.message.text) : ($json.callback_query ? $json.callback_query.data : ($json.chat_member ? 'new_member' : '')) }}", "rules": {"rules": [{"value2": "/start"}, {"value2": "/kick", "output": 1}, {"value2": "/pin", "output": 1}, {"value2": "show_roadmap_public", "output": 2}, {"value2": "new_member", "output": 3}, {"value2": "/start view_investment", "output": 4}]}}, "id": "b1d30c6c-b791-4b07-a268-c68ac8aeee6f", "name": "Main Switch", "type": "n8n-nodes-base.switch", "typeVersion": 1, "position": [-940, 32], "notes": "Routes all incoming events. Note that /kick and /pin go to the same output to be checked for admin rights first."}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT * FROM public.project_roadmap ORDER BY id;", "options": {}}, "id": "8891fc8f-992d-45c1-8a2f-d02c83d58535", "name": "Get Roadmap Data", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-720, 240], "credentials": {"supabase": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"jsCode": "const items = $input.all();\nlet formattedText = '🗺️ **ELOH Processing Public Roadmap**\\n\\n';\nfor (const item of items) {\n  formattedText += `- **${item.json.name}**: ${item.json.status}\\n`;\n}\n\n$input.first().json.formatted_text = formattedText;\nreturn $input.first();"}, "id": "5bde6a07-30c4-4da7-98f8-3e32f3f12bde", "name": "Format Roadmap Text", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-500, 240]}, {"parameters": {"chatId": "={{ $json.callback_query.message.chat.id }}", "text": "={{ $json.formatted_text }}", "additionalFields": {}}, "id": "6fda352b-ab1c-4834-8e01-1653a1882859", "name": "Send Roadmap", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-280, 240], "webhookId": "65af357d-c0e8-4dc8-aa35-8f91762784a9", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"chatId": "={{ $json.chat_member.chat.id }}", "text": "=Welcome @{{ $json.chat_member.new_chat_member.user.username }} to ELOH Processing DAO! 🎉\n\nPlease review our rules and start a private chat for investor features.", "replyMarkup": "inlineKeyboard", "inlineKeyboard": {"rows": [{"row": {"buttons": [{"text": "💬 Start Private Chat", "additionalFields": {"url": "={{ 'https://t.me/' + $node['Set Variables'].json.botUsername }}"}}]}}]}, "additionalFields": {}}, "id": "b0ef5d98-2b21-4af2-8a85-f406646915d6", "name": "Send Welcome", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-720, 440], "webhookId": "9ae15ef7-31a5-4b20-a299-a7dbbdb3b941", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT * FROM public.users WHERE telegram_id = '{{ $json.message.from.id }}' LIMIT 1", "options": {}}, "id": "1caa044e-2ae8-4d8a-b200-69d447300ba2", "name": "Check User in Database", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-720, 560], "credentials": {"supabase": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"conditions": {"options": {}, "conditions": [{"leftValue": "={{ $node[\"Check User in Database\"].json[0]?.is_verified_investor }}", "rightValue": true, "operation": {"type": "boolean", "operation": "true"}}]}, "options": {}}, "id": "e08a8cf0-f204-4244-bf9d-f3c864fad7ef", "name": "Check if Verified Investor", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-500, 560]}, {"parameters": {"chatId": "={{ $json.message.chat.id }}", "text": "=Hello {{ $node[\"Check User in Database\"].json[0].name }}! Your current investment value is: ${{ $node[\"Check User in Database\"].json[0].investment_details.total_value_usd }}.", "additionalFields": {}}, "id": "3ec8d4fc-9d57-408f-93d5-02ee40269501", "name": "Send Investment Details", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-280, 460], "webhookId": "2a151dcc-fc11-477a-accf-388dd945cf76", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"chatId": "={{ $json.message.chat.id }}", "text": "Your Telegram account is not yet linked to a verified investor profile. Please contact support.", "additionalFields": {}}, "id": "d11f3ea4-64b8-4bc4-a5ab-58cbd41b440f", "name": "Send Not Verified Message", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-280, 660], "webhookId": "ab0a81e9-8f5d-4df6-a6a9-958654f2ff15", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT role FROM public.users WHERE telegram_id = '{{$json.message.from.id}}' AND role = 'admin' LIMIT 1;", "options": {}}, "id": "61bbff19-0f0e-4364-9f45-ded9bf1f8ac2", "name": "Check if User is Admin", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-720, 20], "credentials": {"supabase": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"conditions": {"options": {}, "conditions": [{"leftValue": "={{$node['Check if User is Admin'].json.length}}", "rightValue": 0, "operation": {"type": "number", "operation": "larger"}}]}, "options": {}}, "id": "49395ee7-3455-4d80-be2e-30f237649022", "name": "IF Admin", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-500, 20]}, {"parameters": {"chatId": "={{ $json.message.chat.id }}", "text": "Admin command received. This feature is coming soon!", "additionalFields": {}}, "id": "30c94954-451d-436e-a255-e794dd3e763e", "name": "Admin Action Placeholder", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-280, 20], "webhookId": "5b9eb8ba-07ba-49b3-9f01-81b6def74252", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"chatId": "={{ $json.message.chat.id }}", "text": "Welcome to ELOH Processing DAO! 🚀", "replyMarkup": "inlineKeyboard", "inlineKeyboard": {"rows": [{"row": {"buttons": [{"text": "🗺️ Public Roadmap", "additionalFields": {"callbackData": "show_roadmap_public"}}]}}, {"row": {"buttons": [{"text": "💰 View My Investment", "additionalFields": {"url": "https://t.me/elohprocessingbot?start=view_investment"}}]}}]}, "additionalFields": {}}, "id": "df18a1ee-3a04-4c02-8600-f72767618aa6", "name": "Send Start Message", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-720, -180], "webhookId": "63e9fd13-fae6-4adf-8571-cbc80e56671e", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}], "pinData": {}, "connections": {"Telegram Trigger": {"main": [[{"node": "Set Variables", "type": "main", "index": 0}]]}, "Set Variables": {"main": [[{"node": "Main Switch", "type": "main", "index": 0}]]}, "Main Switch": {"main": [[{"node": "Send Start Message", "type": "main", "index": 0}], [{"node": "Check if User is Admin", "type": "main", "index": 0}], [{"node": "Get Roadmap Data", "type": "main", "index": 0}], [{"node": "Send Welcome", "type": "main", "index": 0}], [{"node": "Check User in Database", "type": "main", "index": 0}]]}, "Get Roadmap Data": {"main": [[{"node": "Format Roadmap Text", "type": "main", "index": 0}]]}, "Format Roadmap Text": {"main": [[{"node": "Send Roadmap", "type": "main", "index": 0}]]}, "Check User in Database": {"main": [[{"node": "Check if Verified Investor", "type": "main", "index": 0}]]}, "Check if Verified Investor": {"main": [[{"node": "Send Investment Details", "type": "main", "index": 0}], [{"node": "Send Not Verified Message", "type": "main", "index": 0}]]}, "Check if User is Admin": {"main": [[{"node": "IF Admin", "type": "main", "index": 0}]]}, "IF Admin": {"main": [[{"node": "Admin Action Placeholder", "type": "main", "index": 0}], []]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "18ce542f-a3d6-43dd-8190-3d66eee4341e", "meta": {"templateCredsSetupCompleted": true, "instanceId": "1f52f14222153875e5cd754160d07a91bc57a3dd7ee7266d4266eae0b531fc56"}, "id": "cl97drsgNofoPdxU", "tags": [{"createdAt": "2025-08-06T12:34:09.665Z", "updatedAt": "2025-08-06T12:34:09.665Z", "id": "EKr6mdBmEMIyCP37", "name": "ELOH"}]}