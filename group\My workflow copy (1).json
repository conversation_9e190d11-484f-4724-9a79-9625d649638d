{"name": "My workflow copy", "nodes": [{"parameters": {"updates": ["message", "chat_member", "callback_query"], "additionalFields": {}}, "id": "b36a3c33-6d78-4569-a6aa-9850ad747419", "name": "<PERSON>eg<PERSON>", "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.2, "position": [-752, 240], "webhookId": "76bfb192-83ad-4541-9197-36d47cf7049b", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"values": {"string": [{"name": "botUsername", "value": "elohprocessingbot"}]}, "options": {}}, "id": "dc6de9de-981c-425c-859d-963ffde78a48", "name": "Set Variables", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [-656, 240]}, {"parameters": {"dataType": "string", "value1": "={{ $json.message ? ($json.message.text.includes(' ') ? $json.message.text.split(' ')[0] : $json.message.text) : ($json.callback_query ? $json.callback_query.data : ($json.chat_member ? 'new_member' : '')) }}", "rules": {"rules": [{"value2": "/start"}, {"value2": "/kick", "output": 1}, {"value2": "/pin", "output": 1}, {"value2": "show_roadmap_public", "output": 2}, {"value2": "new_member", "output": 3}, {"value2": "/start view_investment", "output": 3}]}}, "id": "fde67714-e2c5-45ef-ba40-e4a8e29846d5", "name": "Main Switch", "type": "n8n-nodes-base.switch", "typeVersion": 1, "position": [-560, 224], "notes": "Routes all incoming events. Note that /kick and /pin go to the same output to be checked for admin rights first."}, {"parameters": {"operation": "get", "tableId": "project_roadmap", "filters": {"conditions": [{"keyName": "id"}]}}, "id": "933f42d0-12a0-48b1-b6b6-281ea19ae6f8", "name": "Get Roadmap Data", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-288, 352], "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"jsCode": "const items = $input.all();\nlet formattedText = '🗺️ **ELOH Processing Public Roadmap**\\n\\n';\nfor (const item of items) {\n  formattedText += `- **${item.json.name}**: ${item.json.status}\\n`;\n}\n\n$input.first().json.formatted_text = formattedText;\nreturn $input.first();"}, "id": "2c2f7b81-f898-4f1c-aba9-38e182f6ddab", "name": "Format Roadmap Text", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-96, 352]}, {"parameters": {"chatId": "={{ $json.callback_query.message.chat.id }}", "text": "={{ $json.formatted_text }}", "additionalFields": {}}, "id": "808038d9-eed2-4966-ab26-dce59b1f72ed", "name": "Send Roadmap", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [96, 352], "webhookId": "65af357d-c0e8-4dc8-aa35-8f91762784a9", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"chatId": "={{ $json.chat_member.chat.id }}", "text": "=Welcome @{{ $json.chat_member.new_chat_member.user.username }} to ELOH Processing DAO! 🎉\n\nPlease review our rules and start a private chat for investor features.", "replyMarkup": "inlineKeyboard", "inlineKeyboard": {"rows": [{"row": {"buttons": [{"text": "💬 Start Private Chat", "additionalFields": {"url": "={{ 'https://t.me/' + $node['Set Variables'].json.botUsername }}"}}]}}]}, "additionalFields": {}}, "id": "f77eb9fa-5f7a-4155-a2ff-0bab28938dcc", "name": "Send Welcome", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-288, 512], "webhookId": "9ae15ef7-31a5-4b20-a299-a7dbbdb3b941", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"operation": "get", "tableId": "users", "filters": {"conditions": [{"keyName": "telegram_id"}]}}, "id": "6838e0f0-9ae4-40ef-b301-2999ce49bf47", "name": "Check User in Database", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-288, 160], "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"conditions": {"options": {}, "conditions": [{"leftValue": "={{ $node[\"Check User in Database\"].json[0]?.is_verified_investor }}", "rightValue": true, "operation": {"type": "boolean", "operation": "true"}}]}, "options": {}}, "id": "2e6bc2bf-7790-4e96-b249-7d089bec603b", "name": "Check if Verified Investor", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-112, -32]}, {"parameters": {"chatId": "={{ $json.message.chat.id }}", "text": "=Hello {{ $node[\"Check User in Database\"].json[0].name }}! Your current investment value is: ${{ $node[\"Check User in Database\"].json[0].investment_details.total_value_usd }}.", "additionalFields": {}}, "id": "aed1bbe0-4215-46ad-ba1f-5c71ef7565ac", "name": "Send Investment Details", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [320, -192], "webhookId": "2a151dcc-fc11-477a-accf-388dd945cf76", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"chatId": "={{ $json.message.chat.id }}", "text": "Your Telegram account is not yet linked to a verified investor profile. Please contact support.", "additionalFields": {}}, "id": "15ca1d6a-47d0-47b7-84e1-0fd0e957c13c", "name": "Send Not Verified Message", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [320, -32], "webhookId": "ab0a81e9-8f5d-4df6-a6a9-958654f2ff15", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"operation": "get", "tableId": "v_users_admin_check", "filters": {"conditions": [{"keyName": "telegram_id"}]}}, "id": "5f292ffb-dd50-4987-b6fc-96ea97e004b2", "name": "Check if User is Admin", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-48, 176], "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"conditions": {"options": {}, "conditions": [{"leftValue": "={{$node['Check if User is Admin'].json.length}}", "rightValue": 0, "operation": {"type": "number", "operation": "larger"}}]}, "options": {}}, "id": "559f3dff-fc9f-472a-b053-1da09312299d", "name": "IF Admin", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [336, 144]}, {"parameters": {"chatId": "={{ $json.message.chat.id }}", "text": "Admin command received. This feature is coming soon!", "additionalFields": {}}, "id": "40b8899f-c7d0-49cf-8855-90e7972a6246", "name": "Admin Action Placeholder", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [512, 176], "webhookId": "5b9eb8ba-07ba-49b3-9f01-81b6def74252", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"chatId": "={{ $json.message.chat.id }}", "text": "Welcome to ELOH Processing DAO! 🚀", "replyMarkup": "inlineKeyboard", "inlineKeyboard": {"rows": [{"row": {"buttons": [{"text": "🗺️ Public Roadmap", "additionalFields": {"callbackData": "show_roadmap_public"}}]}}, {"row": {"buttons": [{"text": "💰 View My Investment", "additionalFields": {"url": "https://t.me/elohprocessingbot?start=view_investment"}}]}}]}, "additionalFields": {}}, "id": "372c78c2-ada4-41b4-9de4-24d46c8f0fd4", "name": "Send Start Message", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-96, 512], "webhookId": "63e9fd13-fae6-4adf-8571-cbc80e56671e", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}], "pinData": {}, "connections": {"Telegram Trigger": {"main": [[{"node": "Set Variables", "type": "main", "index": 0}]]}, "Set Variables": {"main": [[{"node": "Main Switch", "type": "main", "index": 0}]]}, "Main Switch": {"main": [[{"node": "Send Start Message", "type": "main", "index": 0}], [{"node": "Check User in Database", "type": "main", "index": 0}], [{"node": "Get Roadmap Data", "type": "main", "index": 0}], [{"node": "Send Welcome", "type": "main", "index": 0}]]}, "Get Roadmap Data": {"main": [[{"node": "Format Roadmap Text", "type": "main", "index": 0}]]}, "Format Roadmap Text": {"main": [[{"node": "Send Roadmap", "type": "main", "index": 0}]]}, "Check User in Database": {"main": [[{"node": "Check if Verified Investor", "type": "main", "index": 0}, {"node": "Check if User is Admin", "type": "main", "index": 0}]]}, "Check if Verified Investor": {"main": [[{"node": "Send Investment Details", "type": "main", "index": 0}], [{"node": "Send Not Verified Message", "type": "main", "index": 0}]]}, "Check if User is Admin": {"main": [[{"node": "IF Admin", "type": "main", "index": 0}]]}, "IF Admin": {"main": [[{"node": "Admin Action Placeholder", "type": "main", "index": 0}]]}, "Send Welcome": {"main": [[{"node": "Send Start Message", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "815921b7-7e6f-4ac1-a9d0-fb422f88e797", "meta": {"templateCredsSetupCompleted": true, "instanceId": "1f52f14222153875e5cd754160d07a91bc57a3dd7ee7266d4266eae0b531fc56"}, "id": "yXSgMYBuZvK8mqvY", "tags": []}