{"name": "My Dynamic Workflow", "nodes": [{"parameters": {"updates": ["message", "callback_query", "pre_checkout_query", "successful_payment"], "additionalFields": {}}, "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.2, "position": [-848, -176], "id": "1019379d-3ae6-4ed3-a005-08b4ffef9e90", "name": "<PERSON>eg<PERSON>", "webhookId": "23b39a94-f325-4058-9e19-68f6cc044920", "credentials": {"telegramApi": {"id": "TJrFezJVWaryfEAe", "name": "Telegram account"}}}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "{{$json[\"message\"][\"text\"]}}", "rightValue": "/start", "operator": {"type": "string", "operation": "equals"}, "id": "c4795199-7c79-4b5e-b91b-d77c3e6fc53d"}], "combinator": "and"}, "renameOutput": true, "outputKey": "Welcome"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{$json.callback_query.data}}", "rightValue": "browse_vendors", "operator": {"type": "string", "operation": "equals"}, "id": "2d12e617-640a-4841-9457-37a5f6e8c75e"}], "combinator": "and"}, "renameOutput": true, "outputKey": "BrowseV<PERSON><PERSON>"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{$json.callback_query.data}}", "rightValue": "back_to_vendors", "operator": {"type": "string", "operation": "equals"}, "id": "402e1c9e-64d5-4927-a006-25816b67e1a7"}], "combinator": "and"}, "renameOutput": true, "outputKey": "BackToVendors"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{$json.callback_query.data.startsWith('vendor_')}}", "rightValue": "true", "operator": {"type": "boolean", "operation": "equals"}, "id": "d0407a51-5100-47e0-a929-23136a5a9359"}], "combinator": "and"}, "renameOutput": true, "outputKey": "VendorSelected"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{$json.callback_query.data}}", "rightValue": "menu_", "operator": {"type": "string", "operation": "startsWith"}, "id": "3b680798-251f-495f-a39c-51368942b036"}], "combinator": "and"}, "renameOutput": true, "outputKey": "MenuSelected"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{$json.callback_query.data.startsWith('item_')}}", "rightValue": "true", "operator": {"type": "boolean", "operation": "equals"}, "id": "936b801f-b51f-431e-b873-102a0172e811"}], "combinator": "and"}, "renameOutput": true, "outputKey": "ItemSelected"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{$json.update_type}}", "rightValue": "pre_checkout_query", "operator": {"type": "string", "operation": "equals"}, "id": "e24d2091-6330-4e3a-9774-8b049646b9a2"}], "combinator": "and"}, "renameOutput": true, "outputKey": "PreCheckoutQuery"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{$json.update_type}}", "rightValue": "successful_payment", "operator": {"type": "string", "operation": "equals"}, "id": "673f324d-8b83-4a6c-b3a1-7c98031a26d2"}], "combinator": "and"}, "renameOutput": true, "outputKey": "SuccessfulPayment"}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [-592, -208], "id": "1125a93a-a8ba-4f5d-9cc8-e19118080ce1", "name": "Main Switch"}, {"parameters": {"chatId": "={{ $json.message.from.id }}", "text": "Choose an option below to get started.", "replyMarkup": "inlineKeyboard", "forceReply": {}, "inlineKeyboard": {"rows": [{"row": {"buttons": [{"text": "View Vendors", "additionalFields": {"callback_data": "browse_vendors"}}]}}]}, "replyKeyboardOptions": {}, "replyKeyboardRemove": {}, "additionalFields": {"appendAttribution": false}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-160, -432], "id": "welcome-message-node", "name": "Welcome Message", "credentials": {"telegramApi": {"id": "TJrFezJVWaryfEAe", "name": "Telegram account"}}}, {"parameters": {"resource": "table", "operation": "read", "tableName": "vendors", "returnAll": true, "additionalFields": {}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-376, -272], "id": "supabase-get-vendors", "name": "Get Vendors", "credentials": {"supabaseApi": {"id": "supabase-cred-id", "name": "Supabase Credentials"}}}, {"parameters": {"functionCode": "let vendor_buttons = [];\n\nfor (const vendor of items[0].json.data) {\n  vendor_buttons.push({\n    text: vendor.name,\n    additionalFields: {\n      callback_data: `vendor_${vendor.id}`\n    }\n  });\n}\n\nvendor_buttons.push({\n  text: \"Back to Start\",\n  additionalFields: {\n    callback_data: \"back_to_start\"\n  }\n});\n\nreturn [\n  {\n    json: {\n      text: \"Please select a vendor:\",\n      inline_keyboard: [vendor_buttons]\n    }\n  }\n];"}, "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [-160, -272], "id": "code-generate-vendor-buttons", "name": "Generate V<PERSON><PERSON>"}, {"parameters": {"chatId": "={{ $json.message ? $json.message.from.id : $json.callback_query.from.id }}", "text": "={{ $json.text }}", "replyMarkup": "inlineKeyboard", "inlineKeyboard": {"rows": [{"row": {"buttons": "={{ $json.inline_keyboard[0] }}"}}]}, "additionalFields": {}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [80, -272], "id": "telegram-send-vendor-list", "name": "Send Vendor List"}, {"parameters": {"resource": "table", "operation": "read", "tableName": "vendors", "filter": "id.eq.{{ $json.callback_query.data.replace('vendor_', '') }}", "additionalFields": {}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-376, -88], "id": "supabase-get-vendor-details", "name": "Get Vendor Details"}, {"parameters": {"resource": "table", "operation": "read", "tableName": "menus", "filter": "vendor_id.eq.{{ $json.callback_query.data.replace('vendor_', '') }}", "additionalFields": {}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-160, -88], "id": "supabase-get-vendor-menus", "name": "<PERSON><PERSON><PERSON>"}, {"parameters": {"functionCode": "let vendor = items[0].json.data[0];\nlet menus = items[1].json.data;\n\nlet menuButtons = menus.map(menu => ({\n  text: menu.name,\n  additionalFields: {\n    callback_data: `menu_${menu.id}`\n  }\n}));\n\nlet backButton = {\n  text: \"← Back to Vendors\",\n  additionalFields: {\n    callback_data: \"back_to_vendors\"\n  }\n};\n\nmenuButtons.push(backButton);\n\nlet messageText = `**${vendor.name}**\\n\\n${vendor.description}\\n\\n**Website:** ${vendor.website_url || 'N/A'}\\n\\n*Select a menu category:*`;\n\nreturn [\n  {\n    json: {\n      text: messageText,\n      inline_keyboard: [menuButtons]\n    }\n  }\n];"}, "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [80, -88], "id": "code-generate-menu-buttons", "name": "Generate <PERSON><PERSON>"}, {"parameters": {"chatId": "={{ $json.message ? $json.message.from.id : $json.callback_query.from.id }}", "text": "={{ $json.text }}", "replyMarkup": "inlineKeyboard", "inlineKeyboard": {"rows": [{"row": {"buttons": "={{ $json.inline_keyboard[0] }}"}}]}, "additionalFields": {}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [280, -88], "id": "telegram-send-vendor-details", "name": "Send Vendor <PERSON>"}, {"parameters": {"resource": "table", "operation": "read", "tableName": "menu_items", "filter": "menu_id.eq.{{ $json.callback_query.data.replace('menu_', '') }}", "additionalFields": {"sortColumn": "category", "sortOrder": "ASC"}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-376, 144], "id": "supabase-get-menu-items", "name": "Get Menu Items"}, {"parameters": {"functionCode": "let items = items[0].json.data;\n\nconst categorizedItems = items.reduce((acc, item) => {\n  const category = item.category || 'Uncategorized';\n  if (!acc[category]) {\n    acc[category] = [];\n  }\n  acc[category].push(item);\n  return acc;\n}, {});\n\nlet outputMessage = \"\";\nfor (const category in categorizedItems) {\n  outputMessage += `*${category}*\\n`;\n  categorizedItems[category].forEach(item => {\n    outputMessage += `- ${item.name} ($${item.price.toFixed(2)})\\n`;\n  });\n  outputMessage += \"\\n\";\n}\n\noutputMessage += \"\\n_Tap an item to add to your cart_\\n\";\n\nlet itemButtons = items.map(item => ({\n  text: `${item.name} - $${item.price.toFixed(2)}`,\n  additionalFields: {\n    callback_data: `item_${item.id}`\n  }\n}));\n\nitemButtons.push({\n  text: \"← Back to Menus\",\n  additionalFields: {\n    callback_data: \"back_to_menus\"\n  }\n});\n\nreturn [\n  {\n    json: {\n      text: outputMessage,\n      inline_keyboard: [itemButtons]\n    }\n  }\n];"}, "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [-160, 144], "id": "code-format-menu-items", "name": "Format Menu Items"}, {"parameters": {"chatId": "={{ $json.message ? $json.message.from.id : $json.callback_query.from.id }}", "text": "={{ $json.text }}", "replyMarkup": "inlineKeyboard", "inlineKeyboard": {"rows": [{"row": {"buttons": "={{ $json.inline_keyboard[0] }}"}}]}, "additionalFields": {}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [80, 144], "id": "telegram-send-menu-items", "name": "Send Menu Items"}, {"parameters": {"resource": "table", "operation": "upsert", "tableName": "carts", "data": {"user_id": "={{ $json.message ? $json.message.from.id : $json.callback_query.from.id }}", "items": "={{ $json.new_cart_items }}", "vendor_id": "={{ $json.vendor_id }}"}, "primaryKey": "user_id", "additionalFields": {}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-160, 344], "id": "supabase-upsert-cart", "name": "Upsert Cart"}, {"parameters": {"resource": "table", "operation": "read", "tableName": "menu_items", "filter": "id.eq.{{ $json.callback_query.data.replace('add_', '') }}", "additionalFields": {}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-552, 344], "id": "supabase-get-item-details", "name": "Get Item Details"}, {"parameters": {"resource": "table", "operation": "read", "tableName": "carts", "filter": "user_id.eq.{{ $json.callback_query.from.id }}", "additionalFields": {}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-376, 344], "id": "supabase-get-user-cart", "name": "Get User Cart"}, {"parameters": {"resource": "callback", "queryId": "={{ $json.callback_query.id }}", "additionalFields": {}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [80, 344], "id": "telegram-answer-callback-query", "name": "Answer Callback Query"}, {"parameters": {"functionCode": "const item = items[0].json.data[0];\nconst cartData = cart[0].json.data[0];\n\nlet newCartItems = cartData ? cartData.items : [];\nlet existingItemIndex = newCartItems.findIndex(cartItem => cartItem.id === item.id);\n\nif (existingItemIndex > -1) {\n  newCartItems[existingItemIndex].quantity += 1;\n} else {\n  newCartItems.push({ id: item.id, name: item.name, price: item.price, quantity: 1 });\n}\n\nreturn [\n  {\n    json: {\n      new_cart_items: newCartItems,\n      vendor_id: cartData ? cartData.vendor_id : null,\n      message_text: `${item.name} added to cart!`\n    }\n  }\n];"}, "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [-160, 240], "id": "code-add-item-to-cart", "name": "Add Item to Cart Logic"}, {"parameters": {"resource": "table", "operation": "read", "tableName": "carts", "filter": "user_id.eq.{{ $json.successful_payment.from.id }}", "additionalFields": {}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-552, 648], "id": "supabase-get-final-cart", "name": "Get Final Cart"}, {"parameters": {"resource": "table", "operation": "insert", "tableName": "orders", "data": {"user_id": "={{ $json.successful_payment.from.id }}", "chat_id": "={{ $json.chat_id }}", "vendor_id": "={{ $json.vendor_id }}", "items": "={{ $json.items }}", "total_amount": "={{ $json.total_amount }}", "currency": "={{ $json.currency }}", "payment_status": "paid", "payment_payload": "={{ $json.successful_payment.invoice_payload }}", "telegram_charge_id": "={{ $json.successful_payment.telegram_payment_charge_id }}", "provider_charge_id": "={{ $json.successful_payment.provider_payment_charge_id }}"}, "additionalFields": {}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-160, 648], "id": "supabase-create-order", "name": "Create Order"}, {"parameters": {"functionCode": "const cart = items[0].json.data[0];\n\nif (!cart) {\n  return [{ json: { error: \"Cart not found.\" } }];\n}\n\nlet total = 0;\ncart.items.forEach(item => {\n  total += item.price * item.quantity;\n});\n\nreturn [\n  {\n    json: {\n      chat_id: $json.message ? $json.message.from.id : $json.callback_query.from.id,\n      vendor_id: cart.vendor_id,\n      items: cart.items,\n      total_amount: total,\n      currency: \"USD\", // Or a dynamic currency based on vendor\n      invoice_payload: `order_${Date.now()}`\n    }\n  }\n];"}, "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [-376, 504], "id": "code-prepare-invoice", "name": "Prepare Invoice"}, {"parameters": {"chatId": "={{ $json.chat_id }}", "resource": "invoice", "title": "Your Food Order", "description": "Please review your order and confirm payment.", "payload": "={{ $json.invoice_payload }}", "providerToken": "YOUR_PROVIDER_TOKEN_FROM_BOTFATHER", "currency": "={{ $json.currency }}", "prices": "={{ $json.items }}", "additionalFields": {}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-160, 504], "id": "telegram-send-invoice", "name": "Send Invoice"}, {"parameters": {"resource": "preCheck<PERSON><PERSON><PERSON><PERSON>", "queryId": "={{ $json.pre_checkout_query.id }}", "additionalFields": {"ok": true}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-160, 456], "id": "telegram-answer-pre-checkout-query", "name": "Answer Pre-Checkout Query"}, {"parameters": {"functionCode": "return [\n  {\n    json: {\n      chatId: $json.successful_payment.from.id,\n      text: `✅ Payment successful! Your order has been confirmed.\\n\\nOrder ID: ${$json.successful_payment.invoice_payload}\\nPayment Provider ID: ${$json.successful_payment.provider_payment_charge_id}`\n    }\n  }\n];"}, "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [40, 648], "id": "code-payment-confirmation-message", "name": "Payment Confirmation Message"}, {"parameters": {"chatId": "={{ $json.chatId }}", "text": "={{ $json.text }}", "additionalFields": {}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [240, 648], "id": "telegram-send-confirmation", "name": "Send Confirmation"}], "connections": {"Telegram Trigger": {"main": [[{"node": "Main Switch", "type": "main", "index": 0}]]}, "Main Switch": {"Welcome": [[{"node": "Welcome Message", "type": "main", "index": 0}]], "BrowseVendors": [[{"node": "Get Vendors", "type": "main", "index": 0}]], "BackToVendors": [[{"node": "Get Vendors", "type": "main", "index": 0}]], "VendorSelected": [[{"node": "Get Vendor Details", "type": "main", "index": 0}, {"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}]], "MenuSelected": [[{"node": "Get Menu Items", "type": "main", "index": 0}]], "ItemSelected": [[{"node": "Get Item Details", "type": "main", "index": 0}, {"node": "Get User Cart", "type": "main", "index": 0}]], "PreCheckoutQuery": [[{"node": "Answer Pre-Checkout Query", "type": "main", "index": 0}]], "SuccessfulPayment": [[{"node": "Get Final Cart", "type": "main", "index": 0}]]}, "Welcome Message": {"main": [[]]}, "Get Vendors": {"main": [[{"node": "Generate V<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Generate Vendor Buttons": {"main": [[{"node": "Send Vendor List", "type": "main", "index": 0}]]}, "Send Vendor List": {"main": [[]]}, "Get Vendor Details": {"main": [[{"node": "Generate <PERSON><PERSON>", "type": "main", "index": 0}]]}, "Get Vendor Menus": {"main": [[{"node": "Generate <PERSON><PERSON>", "type": "main", "index": 0}]]}, "Generate Menu Buttons": {"main": [[{"node": "Send Vendor <PERSON>", "type": "main", "index": 0}]]}, "Send Vendor Details": {"main": [[]]}, "Get Menu Items": {"main": [[{"node": "Format Menu Items", "type": "main", "index": 0}]]}, "Format Menu Items": {"main": [[{"node": "Send Menu Items", "type": "main", "index": 0}]]}, "Send Menu Items": {"main": [[]]}, "Supabase-upsert-cart": {"main": [[{"node": "Answer Callback Query", "type": "main", "index": 0}]]}, "Get Item Details": {"main": [[{"node": "Add Item to Cart Logic", "type": "main", "index": 0}]]}, "Get User Cart": {"main": [[{"node": "Add Item to Cart Logic", "type": "main", "index": 0}]]}, "Answer Callback Query": {"main": [[]]}, "Add Item to Cart Logic": {"main": [[{"node": "Supabase-upsert-cart", "type": "main", "index": 0}]]}, "Get Final Cart": {"main": [[{"node": "Create Order", "type": "main", "index": 0}]]}, "Create Order": {"main": [[{"node": "Payment Confirmation Message", "type": "main", "index": 0}]]}, "Prepare Invoice": {"main": [[{"node": "Send Invoice", "type": "main", "index": 0}]]}, "Send Invoice": {"main": [[]]}, "Answer Pre-Checkout Query": {"main": [[]]}, "Payment Confirmation Message": {"main": [[{"node": "Send Confirmation", "type": "main", "index": 0}]]}, "Send Confirmation": {"main": [[]]}}}